ID,id,desc,beyongd,type,value,rewards,pre
1,1,登录游戏,1,1,[1],"[[23,10]]",
2,2,每日商店购买1次,1,3,[1],"[[23,10]]",
3,3,扫荡1次,1,2,[1],"[[23,10]]",
4,4,消耗钻石100,1,5,[100],"[[23,20]]",
5,5,消耗金币500,1,6,[500],"[[23,10]]",
6,6,消耗体力30,1,7,[30],"[[23,20]]",
7,7,看视频1次,1,8,[1],"[[23,20]]",
8,8,累计登录5天,2,1,[5],"[[24,20]]",
9,9,每日商店购买10次,2,3,[10],"[[24,10]]",
10,10,消耗体力100,2,7,[100],"[[24,20]]",
11,11,消耗钻石1000,2,5,[1000],"[[24,20]]",
12,12,扫荡10次,2,2,[10],"[[24,10]]",
13,13,宝箱开启10次,2,11,[10],"[[24,20]]",
1001,1001,累计登录1天,3,1,[1],"[[11,10]]",
1002,1002,累计登录2天,3,1,[2],"[[11,10]]",1001
1003,1003,累计登录3天,3,1,[3],"[[11,10]]",1002
1004,1004,累计登录4天,3,1,[4],"[[11,10]]",1003
1005,1005,累计登录5天,3,1,[5],"[[11,10]]",1004
1006,1006,累计登录6天,3,1,[6],"[[11,10]]",1005
1007,1007,累计登录7天,3,1,[7],"[[11,10]]",1006
1008,1008,累计登录8天,3,1,[8],"[[11,10]]",1007
1009,1009,累计登录9天,3,1,[9],"[[11,10]]",1008
1010,1010,累计登录10天,3,1,[10],"[[11,10]]",1009
1011,1011,累计登录11天,3,1,[11],"[[11,10]]",1010
1012,1012,累计登录12天,3,1,[12],"[[11,10]]",1011
1013,1013,累计登录13天,3,1,[13],"[[11,10]]",1012
1014,1014,累计登录14天,3,1,[14],"[[11,10]]",1013
1015,1015,累计登录15天,3,1,[15],"[[11,10]]",1014
1016,1016,累计登录16天,3,1,[16],"[[11,10]]",1015
1017,1017,累计登录17天,3,1,[17],"[[11,10]]",1016
1018,1018,累计登录18天,3,1,[18],"[[11,10]]",1017
1019,1019,累计登录19天,3,1,[19],"[[11,10]]",1018
1020,1020,累计登录20天,3,1,[20],"[[11,10]]",1019
1021,1021,累计登录21天,3,1,[21],"[[11,10]]",1020
1022,1022,累计登录22天,3,1,[22],"[[11,10]]",1021
1023,1023,累计登录23天,3,1,[23],"[[11,10]]",1022
1024,1024,累计登录24天,3,1,[24],"[[11,10]]",1023
1025,1025,累计登录25天,3,1,[25],"[[11,10]]",1024
1026,1026,累计登录26天,3,1,[26],"[[11,10]]",1025
1027,1027,累计登录27天,3,1,[27],"[[11,10]]",1026
1028,1028,累计登录28天,3,1,[28],"[[11,10]]",1027
1029,1029,累计登录29天,3,1,[29],"[[11,10]]",1028
1030,1030,累计登录30天,3,1,[30],"[[11,10]]",1029
2000,2000,通关主线第1关,3,10,[1],"[[11,10]]",
2001,2001,通关主线第2关,3,10,[2],"[[11,10]]",2000
2002,2002,通关主线第3关,3,10,[3],"[[11,10]]",2001
2003,2003,通关主线第4关,3,10,[4],"[[11,10]]",2002
2004,2004,通关主线第5关,3,10,[5],"[[11,10]]",2003
2005,2005,通关主线第6关,3,10,[6],"[[11,10]]",2004
2006,2006,通关主线第7关,3,10,[7],"[[11,10]]",2005
2007,2007,通关主线第8关,3,10,[8],"[[11,10]]",2006
2008,2008,通关主线第9关,3,10,[9],"[[11,10]]",2007
2009,2009,通关主线第10关,3,10,[10],"[[11,10]]",2008
2010,2010,通关主线第11关,3,10,[11],"[[11,10]]",2009
2011,2011,通关主线第12关,3,10,[12],"[[11,10]]",2010
2012,2012,通关主线第13关,3,10,[13],"[[11,10]]",2011
2013,2013,通关主线第14关,3,10,[14],"[[11,10]]",2012
2014,2014,通关主线第15关,3,10,[15],"[[11,10]]",2013
2015,2015,通关主线第16关,3,10,[16],"[[11,10]]",2014
2016,2016,通关主线第17关,3,10,[17],"[[11,10]]",2015
2017,2017,通关主线第18关,3,10,[18],"[[11,10]]",2016
2018,2018,通关主线第19关,3,10,[19],"[[11,10]]",2017
2019,2019,通关主线第20关,3,10,[20],"[[11,10]]",2018
2020,2020,通关主线第21关,3,10,[21],"[[11,10]]",2019
2021,2021,通关主线第22关,3,10,[22],"[[11,10]]",2020
2022,2022,通关主线第23关,3,10,[23],"[[11,10]]",2021
2023,2023,通关主线第24关,3,10,[24],"[[11,10]]",2022
2024,2024,通关主线第25关,3,10,[25],"[[11,10]]",2023
2025,2025,通关主线第26关,3,10,[26],"[[11,10]]",2024
2026,2026,通关主线第27关,3,10,[27],"[[11,10]]",2025
2027,2027,通关主线第28关,3,10,[28],"[[11,10]]",2026
2028,2028,通关主线第29关,3,10,[29],"[[11,10]]",2027
2029,2029,通关主线第30关,3,10,[30],"[[11,10]]",2028
2030,2030,通关主线第31关,3,10,[31],"[[11,10]]",2029
2031,2031,通关主线第32关,3,10,[32],"[[11,10]]",2030
2032,2032,通关主线第33关,3,10,[33],"[[11,10]]",2031
2033,2033,通关主线第34关,3,10,[34],"[[11,10]]",2032
2034,2034,通关主线第35关,3,10,[35],"[[11,10]]",2033
2035,2035,通关主线第36关,3,10,[36],"[[11,10]]",2034
2036,2036,通关主线第37关,3,10,[37],"[[11,10]]",2035
2037,2037,通关主线第38关,3,10,[38],"[[11,10]]",2036
2038,2038,通关主线第39关,3,10,[39],"[[11,10]]",2037
2039,2039,通关主线第40关,3,10,[40],"[[11,10]]",2038
2040,2040,通关主线第41关,3,10,[41],"[[11,10]]",2039
2041,2041,通关主线第42关,3,10,[42],"[[11,10]]",2040
2042,2042,通关主线第43关,3,10,[43],"[[11,10]]",2041
2043,2043,通关主线第44关,3,10,[44],"[[11,10]]",2042
2044,2044,通关主线第45关,3,10,[45],"[[11,10]]",2043
2045,2045,通关主线第46关,3,10,[46],"[[11,10]]",2044
2046,2046,通关主线第47关,3,10,[47],"[[11,10]]",2045
2047,2047,通关主线第48关,3,10,[48],"[[11,10]]",2046
2048,2048,通关主线第49关,3,10,[49],"[[11,10]]",2047
2049,2049,通关主线第50关,3,10,[50],"[[11,10]]",2048
2050,2050,通关主线第51关,3,10,[51],"[[11,10]]",2049
2051,2051,通关主线第52关,3,10,[52],"[[11,10]]",2050
2052,2052,通关主线第53关,3,10,[53],"[[11,10]]",2051
2053,2053,通关主线第54关,3,10,[54],"[[11,10]]",2052
2054,2054,通关主线第55关,3,10,[55],"[[11,10]]",2053
2055,2055,通关主线第56关,3,10,[56],"[[11,10]]",2054
2056,2056,通关主线第57关,3,10,[57],"[[11,10]]",2055
2057,2057,通关主线第58关,3,10,[58],"[[11,10]]",2056
2058,2058,通关主线第59关,3,10,[59],"[[11,10]]",2057
2059,2059,通关主线第60关,3,10,[60],"[[11,10]]",2058
2060,2060,通关主线第61关,3,10,[61],"[[11,10]]",2059
2061,2061,通关主线第62关,3,10,[62],"[[11,10]]",2060
2062,2062,通关主线第63关,3,10,[63],"[[11,10]]",2061
2063,2063,通关主线第64关,3,10,[64],"[[11,10]]",2062
2064,2064,通关主线第65关,3,10,[65],"[[11,10]]",2063
2065,2065,通关主线第66关,3,10,[66],"[[11,10]]",2064
2066,2066,通关主线第67关,3,10,[67],"[[11,10]]",2065
2067,2067,通关主线第68关,3,10,[68],"[[11,10]]",2066
2068,2068,通关主线第69关,3,10,[69],"[[11,10]]",2067
2069,2069,通关主线第70关,3,10,[70],"[[11,10]]",2068
3000,3000,扫荡5次,3,2,[5],"[[11,10]]",
3001,3001,扫荡10次,3,2,[10],"[[11,10]]",3000
3002,3002,扫荡15次,3,2,[15],"[[11,10]]",3001
3003,3003,扫荡20次,3,2,[20],"[[11,10]]",3002
3004,3004,扫荡25次,3,2,[25],"[[11,10]]",3003
3005,3005,扫荡30次,3,2,[30],"[[11,10]]",3004
3006,3006,扫荡35次,3,2,[35],"[[11,10]]",3005
3007,3007,扫荡40次,3,2,[40],"[[11,10]]",3006
3008,3008,扫荡45次,3,2,[45],"[[11,10]]",3007
3009,3009,扫荡50次,3,2,[50],"[[11,10]]",3008
3010,3010,扫荡55次,3,2,[55],"[[11,10]]",3009
3011,3011,扫荡60次,3,2,[60],"[[11,10]]",3010
3012,3012,扫荡65次,3,2,[65],"[[11,10]]",3011
3013,3013,扫荡70次,3,2,[70],"[[11,10]]",3012
3014,3014,扫荡75次,3,2,[75],"[[11,10]]",3013
3015,3015,扫荡80次,3,2,[80],"[[11,10]]",3014
3016,3016,扫荡85次,3,2,[85],"[[11,10]]",3015
3017,3017,扫荡90次,3,2,[90],"[[11,10]]",3016
3018,3018,扫荡95次,3,2,[95],"[[11,10]]",3017
3019,3019,扫荡100次,3,2,[100],"[[11,10]]",3018
3020,3020,扫荡105次,3,2,[105],"[[11,10]]",3019
3021,3021,扫荡110次,3,2,[110],"[[11,10]]",3020
3022,3022,扫荡115次,3,2,[115],"[[11,10]]",3021
3023,3023,扫荡120次,3,2,[120],"[[11,10]]",3022
3024,3024,扫荡125次,3,2,[125],"[[11,10]]",3023
3025,3025,扫荡130次,3,2,[130],"[[11,10]]",3024
3026,3026,扫荡135次,3,2,[135],"[[11,10]]",3025
3027,3027,扫荡140次,3,2,[140],"[[11,10]]",3026
3028,3028,扫荡145次,3,2,[145],"[[11,10]]",3027
3029,3029,扫荡150次,3,2,[150],"[[11,10]]",3028
3030,3030,扫荡155次,3,2,[155],"[[11,10]]",3029
3031,3031,扫荡160次,3,2,[160],"[[11,10]]",3030
3032,3032,扫荡165次,3,2,[165],"[[11,10]]",3031
3033,3033,扫荡170次,3,2,[170],"[[11,10]]",3032
3034,3034,扫荡175次,3,2,[175],"[[11,10]]",3033
3035,3035,扫荡180次,3,2,[180],"[[11,10]]",3034
3036,3036,扫荡185次,3,2,[185],"[[11,10]]",3035
3037,3037,扫荡190次,3,2,[190],"[[11,10]]",3036
3038,3038,扫荡195次,3,2,[195],"[[11,10]]",3037
3039,3039,扫荡200次,3,2,[200],"[[11,10]]",3038
3040,3040,扫荡205次,3,2,[205],"[[11,10]]",3039
3041,3041,扫荡210次,3,2,[210],"[[11,10]]",3040
3042,3042,扫荡215次,3,2,[215],"[[11,10]]",3041
3043,3043,扫荡220次,3,2,[220],"[[11,10]]",3042
3044,3044,扫荡225次,3,2,[225],"[[11,10]]",3043
3045,3045,扫荡230次,3,2,[230],"[[11,10]]",3044
3046,3046,扫荡235次,3,2,[235],"[[11,10]]",3045
3047,3047,扫荡240次,3,2,[240],"[[11,10]]",3046
3048,3048,扫荡245次,3,2,[245],"[[11,10]]",3047
3049,3049,扫荡250次,3,2,[250],"[[11,10]]",3048
3050,3050,扫荡255次,3,2,[255],"[[11,10]]",3049
3051,3051,扫荡260次,3,2,[260],"[[11,10]]",3050
3052,3052,扫荡265次,3,2,[265],"[[11,10]]",3051
3053,3053,扫荡270次,3,2,[270],"[[11,10]]",3052
3054,3054,扫荡275次,3,2,[275],"[[11,10]]",3053
3055,3055,扫荡280次,3,2,[280],"[[11,10]]",3054
3056,3056,扫荡285次,3,2,[285],"[[11,10]]",3055
3057,3057,扫荡290次,3,2,[290],"[[11,10]]",3056
3058,3058,扫荡295次,3,2,[295],"[[11,10]]",3057
3059,3059,扫荡300次,3,2,[300],"[[11,10]]",3058
3060,3060,扫荡305次,3,2,[305],"[[11,10]]",3059
3061,3061,扫荡310次,3,2,[310],"[[11,10]]",3060
3062,3062,扫荡315次,3,2,[315],"[[11,10]]",3061
3063,3063,扫荡320次,3,2,[320],"[[11,10]]",3062
3064,3064,扫荡325次,3,2,[325],"[[11,10]]",3063
3065,3065,扫荡330次,3,2,[330],"[[11,10]]",3064
3066,3066,扫荡335次,3,2,[335],"[[11,10]]",3065
3067,3067,扫荡340次,3,2,[340],"[[11,10]]",3066
3068,3068,扫荡345次,3,2,[345],"[[11,10]]",3067
3069,3069,扫荡350次,3,2,[350],"[[11,10]]",3068
3070,3070,扫荡355次,3,2,[355],"[[11,10]]",3069
3071,3071,扫荡360次,3,2,[360],"[[11,10]]",3070
3072,3072,扫荡365次,3,2,[365],"[[11,10]]",3071
3073,3073,扫荡370次,3,2,[370],"[[11,10]]",3072
3074,3074,扫荡375次,3,2,[375],"[[11,10]]",3073
3075,3075,扫荡380次,3,2,[380],"[[11,10]]",3074
3076,3076,扫荡385次,3,2,[385],"[[11,10]]",3075
3077,3077,扫荡390次,3,2,[390],"[[11,10]]",3076
3078,3078,扫荡395次,3,2,[395],"[[11,10]]",3077
3079,3079,扫荡400次,3,2,[400],"[[11,10]]",3078
3080,3080,扫荡405次,3,2,[405],"[[11,10]]",3079
3081,3081,扫荡410次,3,2,[410],"[[11,10]]",3080
3082,3082,扫荡415次,3,2,[415],"[[11,10]]",3081
3083,3083,扫荡420次,3,2,[420],"[[11,10]]",3082
3084,3084,扫荡425次,3,2,[425],"[[11,10]]",3083
3085,3085,扫荡430次,3,2,[430],"[[11,10]]",3084
3086,3086,扫荡435次,3,2,[435],"[[11,10]]",3085
3087,3087,扫荡440次,3,2,[440],"[[11,10]]",3086
3088,3088,扫荡445次,3,2,[445],"[[11,10]]",3087
3089,3089,扫荡450次,3,2,[450],"[[11,10]]",3088
4000,4000,消耗钻石700,3,5,[700],"[[11,10]]",
4001,4001,消耗钻石1400,3,5,[1400],"[[11,10]]",4000
4002,4002,消耗钻石2100,3,5,[2100],"[[11,10]]",4001
4003,4003,消耗钻石2800,3,5,[2800],"[[11,10]]",4002
4004,4004,消耗钻石3500,3,5,[3500],"[[11,10]]",4003
4005,4005,消耗钻石4200,3,5,[4200],"[[11,10]]",4004
4006,4006,消耗钻石4900,3,5,[4900],"[[11,10]]",4005
4007,4007,消耗钻石5600,3,5,[5600],"[[11,10]]",4006
4008,4008,消耗钻石6300,3,5,[6300],"[[11,10]]",4007
4009,4009,消耗钻石7000,3,5,[7000],"[[11,10]]",4008
4010,4010,消耗钻石7700,3,5,[7700],"[[11,10]]",4009
4011,4011,消耗钻石8400,3,5,[8400],"[[11,10]]",4010
4012,4012,消耗钻石9100,3,5,[9100],"[[11,10]]",4011
4013,4013,消耗钻石9800,3,5,[9800],"[[11,10]]",4012
4014,4014,消耗钻石10500,3,5,[10500],"[[11,10]]",4013
4015,4015,消耗钻石11200,3,5,[11200],"[[11,10]]",4014
4016,4016,消耗钻石11900,3,5,[11900],"[[11,10]]",4015
4017,4017,消耗钻石12600,3,5,[12600],"[[11,10]]",4016
4018,4018,消耗钻石13300,3,5,[13300],"[[11,10]]",4017
4019,4019,消耗钻石14000,3,5,[14000],"[[11,10]]",4018
4020,4020,消耗钻石14700,3,5,[14700],"[[11,10]]",4019
4021,4021,消耗钻石15400,3,5,[15400],"[[11,10]]",4020
4022,4022,消耗钻石16100,3,5,[16100],"[[11,10]]",4021
4023,4023,消耗钻石16800,3,5,[16800],"[[11,10]]",4022
4024,4024,消耗钻石17500,3,5,[17500],"[[11,10]]",4023
4025,4025,消耗钻石18200,3,5,[18200],"[[11,10]]",4024
4026,4026,消耗钻石18900,3,5,[18900],"[[11,10]]",4025
4027,4027,消耗钻石19600,3,5,[19600],"[[11,10]]",4026
4028,4028,消耗钻石20300,3,5,[20300],"[[11,10]]",4027
4029,4029,消耗钻石21000,3,5,[21000],"[[11,10]]",4028
4030,4030,消耗钻石21700,3,5,[21700],"[[11,10]]",4029
4031,4031,消耗钻石22400,3,5,[22400],"[[11,10]]",4030
4032,4032,消耗钻石23100,3,5,[23100],"[[11,10]]",4031
4033,4033,消耗钻石23800,3,5,[23800],"[[11,10]]",4032
4034,4034,消耗钻石24500,3,5,[24500],"[[11,10]]",4033
4035,4035,消耗钻石25200,3,5,[25200],"[[11,10]]",4034
4036,4036,消耗钻石25900,3,5,[25900],"[[11,10]]",4035
4037,4037,消耗钻石26600,3,5,[26600],"[[11,10]]",4036
4038,4038,消耗钻石27300,3,5,[27300],"[[11,10]]",4037
4039,4039,消耗钻石28000,3,5,[28000],"[[11,10]]",4038
4040,4040,消耗钻石28700,3,5,[28700],"[[11,10]]",4039
4041,4041,消耗钻石29400,3,5,[29400],"[[11,10]]",4040
4042,4042,消耗钻石30100,3,5,[30100],"[[11,10]]",4041
4043,4043,消耗钻石30800,3,5,[30800],"[[11,10]]",4042
4044,4044,消耗钻石31500,3,5,[31500],"[[11,10]]",4043
4045,4045,消耗钻石32200,3,5,[32200],"[[11,10]]",4044
4046,4046,消耗钻石32900,3,5,[32900],"[[11,10]]",4045
4047,4047,消耗钻石33600,3,5,[33600],"[[11,10]]",4046
4048,4048,消耗钻石34300,3,5,[34300],"[[11,10]]",4047
4049,4049,消耗钻石35000,3,5,[35000],"[[11,10]]",4048
4050,4050,消耗钻石35700,3,5,[35700],"[[11,10]]",4049
4051,4051,消耗钻石36400,3,5,[36400],"[[11,10]]",4050
4052,4052,消耗钻石37100,3,5,[37100],"[[11,10]]",4051
4053,4053,消耗钻石37800,3,5,[37800],"[[11,10]]",4052
4054,4054,消耗钻石38500,3,5,[38500],"[[11,10]]",4053
4055,4055,消耗钻石39200,3,5,[39200],"[[11,10]]",4054
4056,4056,消耗钻石39900,3,5,[39900],"[[11,10]]",4055
4057,4057,消耗钻石40600,3,5,[40600],"[[11,10]]",4056
4058,4058,消耗钻石41300,3,5,[41300],"[[11,10]]",4057
4059,4059,消耗钻石42000,3,5,[42000],"[[11,10]]",4058
4060,4060,消耗钻石42700,3,5,[42700],"[[11,10]]",4059
4061,4061,消耗钻石1000,3,5,[1000],"[[11,10]]",
4062,4062,消耗钻石2000,3,5,[2000],"[[11,10]]",4061
4063,4063,消耗钻石3000,3,5,[3000],"[[11,10]]",4062
4064,4064,消耗钻石4000,3,5,[4000],"[[11,10]]",4063
4065,4065,消耗钻石5000,3,5,[5000],"[[11,10]]",4064
4066,4066,消耗钻石6000,3,5,[6000],"[[11,10]]",4065
4067,4067,消耗钻石7000,3,5,[7000],"[[11,10]]",4066
4068,4068,消耗钻石8000,3,5,[8000],"[[11,10]]",4067
4069,4069,消耗钻石9000,3,5,[9000],"[[11,10]]",4068
4070,4070,消耗钻石10000,3,5,[10000],"[[11,10]]",4069
4071,4071,消耗钻石11000,3,5,[11000],"[[11,10]]",4070
4072,4072,消耗钻石12000,3,5,[12000],"[[11,10]]",4071
4073,4073,消耗钻石13000,3,5,[13000],"[[11,10]]",4072
4074,4074,消耗钻石14000,3,5,[14000],"[[11,10]]",4073
4075,4075,消耗钻石15000,3,5,[15000],"[[11,10]]",4074
4076,4076,消耗钻石16000,3,5,[16000],"[[11,10]]",4075
4077,4077,消耗钻石17000,3,5,[17000],"[[11,10]]",4076
4078,4078,消耗钻石18000,3,5,[18000],"[[11,10]]",4077
4079,4079,消耗钻石19000,3,5,[19000],"[[11,10]]",4078
4080,4080,消耗钻石20000,3,5,[20000],"[[11,10]]",4079
4081,4081,消耗钻石21000,3,5,[21000],"[[11,10]]",4080
4082,4082,消耗钻石22000,3,5,[22000],"[[11,10]]",4081
4083,4083,消耗钻石23000,3,5,[23000],"[[11,10]]",4082
4084,4084,消耗钻石24000,3,5,[24000],"[[11,10]]",4083
4085,4085,消耗钻石25000,3,5,[25000],"[[11,10]]",4084
4086,4086,消耗钻石26000,3,5,[26000],"[[11,10]]",4085
4087,4087,消耗钻石27000,3,5,[27000],"[[11,10]]",4086
4088,4088,消耗钻石28000,3,5,[28000],"[[11,10]]",4087
4089,4089,消耗钻石29000,3,5,[29000],"[[11,10]]",4088
4090,4090,消耗钻石30000,3,5,[30000],"[[11,10]]",4089
4091,4091,消耗钻石31000,3,5,[31000],"[[11,10]]",4090
4092,4092,消耗钻石32000,3,5,[32000],"[[11,10]]",4091
4093,4093,消耗钻石33000,3,5,[33000],"[[11,10]]",4092
4094,4094,消耗钻石34000,3,5,[34000],"[[11,10]]",4093
4095,4095,消耗钻石35000,3,5,[35000],"[[11,10]]",4094
4096,4096,消耗钻石36000,3,5,[36000],"[[11,10]]",4095
4097,4097,消耗钻石37000,3,5,[37000],"[[11,10]]",4096
4098,4098,消耗钻石38000,3,5,[38000],"[[11,10]]",4097
4099,4099,消耗钻石39000,3,5,[39000],"[[11,10]]",4098
4100,4100,消耗钻石40000,3,5,[40000],"[[11,10]]",4099
4101,4101,消耗钻石41000,3,5,[41000],"[[11,10]]",4100
4102,4102,消耗钻石42000,3,5,[42000],"[[11,10]]",4101
4103,4103,消耗钻石43000,3,5,[43000],"[[11,10]]",4102
4104,4104,消耗钻石44000,3,5,[44000],"[[11,10]]",4103
4105,4105,消耗钻石45000,3,5,[45000],"[[11,10]]",4104
4106,4106,消耗钻石46000,3,5,[46000],"[[11,10]]",4105
4107,4107,消耗钻石47000,3,5,[47000],"[[11,10]]",4106
4108,4108,消耗钻石48000,3,5,[48000],"[[11,10]]",4107
4109,4109,消耗钻石49000,3,5,[49000],"[[11,10]]",4108
4110,4110,消耗钻石50000,3,5,[50000],"[[11,10]]",4109
4111,4111,消耗钻石51000,3,5,[51000],"[[11,10]]",4110
4112,4112,消耗钻石52000,3,5,[52000],"[[11,10]]",4111
4113,4113,消耗钻石53000,3,5,[53000],"[[11,10]]",4112
4114,4114,消耗钻石54000,3,5,[54000],"[[11,10]]",4113
4115,4115,消耗钻石55000,3,5,[55000],"[[11,10]]",4114
4116,4116,消耗钻石56000,3,5,[56000],"[[11,10]]",4115
4117,4117,消耗钻石57000,3,5,[57000],"[[11,10]]",4116
4118,4118,消耗钻石58000,3,5,[58000],"[[11,10]]",4117
4119,4119,消耗钻石59000,3,5,[59000],"[[11,10]]",4118
4120,4120,消耗钻石60000,3,5,[60000],"[[11,10]]",4119
5000,5000,消耗金币7000,3,6,[7000],"[[11,10]]",
5001,5001,消耗金币14000,3,6,[14000],"[[11,10]]",5000
5002,5002,消耗金币21000,3,6,[21000],"[[11,10]]",5001
5003,5003,消耗金币28000,3,6,[28000],"[[11,10]]",5002
5004,5004,消耗金币35000,3,6,[35000],"[[11,10]]",5003
5005,5005,消耗金币42000,3,6,[42000],"[[11,10]]",5004
5006,5006,消耗金币49000,3,6,[49000],"[[11,10]]",5005
5007,5007,消耗金币56000,3,6,[56000],"[[11,10]]",5006
5008,5008,消耗金币63000,3,6,[63000],"[[11,10]]",5007
5009,5009,消耗金币70000,3,6,[70000],"[[11,10]]",5008
5010,5010,消耗金币77000,3,6,[77000],"[[11,10]]",5009
5011,5011,消耗金币84000,3,6,[84000],"[[11,10]]",5010
5012,5012,消耗金币91000,3,6,[91000],"[[11,10]]",5011
5013,5013,消耗金币98000,3,6,[98000],"[[11,10]]",5012
5014,5014,消耗金币105000,3,6,[105000],"[[11,10]]",5013
5015,5015,消耗金币112000,3,6,[112000],"[[11,10]]",5014
5016,5016,消耗金币119000,3,6,[119000],"[[11,10]]",5015
5017,5017,消耗金币126000,3,6,[126000],"[[11,10]]",5016
5018,5018,消耗金币133000,3,6,[133000],"[[11,10]]",5017
5019,5019,消耗金币140000,3,6,[140000],"[[11,10]]",5018
5020,5020,消耗金币147000,3,6,[147000],"[[11,10]]",5019
5021,5021,消耗金币154000,3,6,[154000],"[[11,10]]",5020
5022,5022,消耗金币161000,3,6,[161000],"[[11,10]]",5021
5023,5023,消耗金币168000,3,6,[168000],"[[11,10]]",5022
5024,5024,消耗金币175000,3,6,[175000],"[[11,10]]",5023
5025,5025,消耗金币182000,3,6,[182000],"[[11,10]]",5024
5026,5026,消耗金币189000,3,6,[189000],"[[11,10]]",5025
5027,5027,消耗金币196000,3,6,[196000],"[[11,10]]",5026
5028,5028,消耗金币203000,3,6,[203000],"[[11,10]]",5027
5029,5029,消耗金币210000,3,6,[210000],"[[11,10]]",5028
5030,5030,消耗金币217000,3,6,[217000],"[[11,10]]",5029
5031,5031,消耗金币224000,3,6,[224000],"[[11,10]]",5030
5032,5032,消耗金币231000,3,6,[231000],"[[11,10]]",5031
5033,5033,消耗金币238000,3,6,[238000],"[[11,10]]",5032
5034,5034,消耗金币245000,3,6,[245000],"[[11,10]]",5033
5035,5035,消耗金币252000,3,6,[252000],"[[11,10]]",5034
5036,5036,消耗金币259000,3,6,[259000],"[[11,10]]",5035
5037,5037,消耗金币266000,3,6,[266000],"[[11,10]]",5036
5038,5038,消耗金币273000,3,6,[273000],"[[11,10]]",5037
5039,5039,消耗金币280000,3,6,[280000],"[[11,10]]",5038
5040,5040,消耗金币287000,3,6,[287000],"[[11,10]]",5039
5041,5041,消耗金币294000,3,6,[294000],"[[11,10]]",5040
5042,5042,消耗金币301000,3,6,[301000],"[[11,10]]",5041
5043,5043,消耗金币308000,3,6,[308000],"[[11,10]]",5042
5044,5044,消耗金币315000,3,6,[315000],"[[11,10]]",5043
5045,5045,消耗金币322000,3,6,[322000],"[[11,10]]",5044
5046,5046,消耗金币329000,3,6,[329000],"[[11,10]]",5045
5047,5047,消耗金币336000,3,6,[336000],"[[11,10]]",5046
5048,5048,消耗金币343000,3,6,[343000],"[[11,10]]",5047
5049,5049,消耗金币350000,3,6,[350000],"[[11,10]]",5048
5050,5050,消耗金币357000,3,6,[357000],"[[11,10]]",5049
5051,5051,消耗金币364000,3,6,[364000],"[[11,10]]",5050
5052,5052,消耗金币371000,3,6,[371000],"[[11,10]]",5051
5053,5053,消耗金币378000,3,6,[378000],"[[11,10]]",5052
5054,5054,消耗金币385000,3,6,[385000],"[[11,10]]",5053
5055,5055,消耗金币392000,3,6,[392000],"[[11,10]]",5054
5056,5056,消耗金币399000,3,6,[399000],"[[11,10]]",5055
5057,5057,消耗金币406000,3,6,[406000],"[[11,10]]",5056
5058,5058,消耗金币413000,3,6,[413000],"[[11,10]]",5057
5059,5059,消耗金币420000,3,6,[420000],"[[11,10]]",5058
5060,5060,消耗金币427000,3,6,[427000],"[[11,10]]",5059
5061,5061,消耗金币434000,3,6,[434000],"[[11,10]]",5060
5062,5062,消耗金币10000,3,6,[10000],"[[11,10]]",
5063,5063,消耗金币20000,3,6,[20000],"[[11,10]]",5062
5064,5064,消耗金币30000,3,6,[30000],"[[11,10]]",5063
5065,5065,消耗金币40000,3,6,[40000],"[[11,10]]",5064
5066,5066,消耗金币50000,3,6,[50000],"[[11,10]]",5065
5067,5067,消耗金币60000,3,6,[60000],"[[11,10]]",5066
5068,5068,消耗金币70000,3,6,[70000],"[[11,10]]",5067
5069,5069,消耗金币80000,3,6,[80000],"[[11,10]]",5068
5070,5070,消耗金币90000,3,6,[90000],"[[11,10]]",5069
5071,5071,消耗金币100000,3,6,[100000],"[[11,10]]",5070
5072,5072,消耗金币110000,3,6,[110000],"[[11,10]]",5071
5073,5073,消耗金币120000,3,6,[120000],"[[11,10]]",5072
5074,5074,消耗金币130000,3,6,[130000],"[[11,10]]",5073
5075,5075,消耗金币140000,3,6,[140000],"[[11,10]]",5074
5076,5076,消耗金币150000,3,6,[150000],"[[11,10]]",5075
5077,5077,消耗金币160000,3,6,[160000],"[[11,10]]",5076
5078,5078,消耗金币170000,3,6,[170000],"[[11,10]]",5077
5079,5079,消耗金币180000,3,6,[180000],"[[11,10]]",5078
5080,5080,消耗金币190000,3,6,[190000],"[[11,10]]",5079
5081,5081,消耗金币200000,3,6,[200000],"[[11,10]]",5080
5082,5082,消耗金币210000,3,6,[210000],"[[11,10]]",5081
5083,5083,消耗金币220000,3,6,[220000],"[[11,10]]",5082
5084,5084,消耗金币230000,3,6,[230000],"[[11,10]]",5083
5085,5085,消耗金币240000,3,6,[240000],"[[11,10]]",5084
5086,5086,消耗金币250000,3,6,[250000],"[[11,10]]",5085
5087,5087,消耗金币260000,3,6,[260000],"[[11,10]]",5086
5088,5088,消耗金币270000,3,6,[270000],"[[11,10]]",5087
5089,5089,消耗金币280000,3,6,[280000],"[[11,10]]",5088
5090,5090,消耗金币290000,3,6,[290000],"[[11,10]]",5089
5091,5091,消耗金币300000,3,6,[300000],"[[11,10]]",5090
5092,5092,消耗金币310000,3,6,[310000],"[[11,10]]",5091
5093,5093,消耗金币320000,3,6,[320000],"[[11,10]]",5092
5094,5094,消耗金币330000,3,6,[330000],"[[11,10]]",5093
5095,5095,消耗金币340000,3,6,[340000],"[[11,10]]",5094
5096,5096,消耗金币350000,3,6,[350000],"[[11,10]]",5095
5097,5097,消耗金币360000,3,6,[360000],"[[11,10]]",5096
5098,5098,消耗金币370000,3,6,[370000],"[[11,10]]",5097
5099,5099,消耗金币380000,3,6,[380000],"[[11,10]]",5098
5100,5100,消耗金币390000,3,6,[390000],"[[11,10]]",5099
5101,5101,消耗金币400000,3,6,[400000],"[[11,10]]",5100
5102,5102,消耗金币410000,3,6,[410000],"[[11,10]]",5101
5103,5103,消耗金币420000,3,6,[420000],"[[11,10]]",5102
5104,5104,消耗金币430000,3,6,[430000],"[[11,10]]",5103
5105,5105,消耗金币440000,3,6,[440000],"[[11,10]]",5104
5106,5106,消耗金币450000,3,6,[450000],"[[11,10]]",5105
5107,5107,消耗金币460000,3,6,[460000],"[[11,10]]",5106
5108,5108,消耗金币470000,3,6,[470000],"[[11,10]]",5107
5109,5109,消耗金币480000,3,6,[480000],"[[11,10]]",5108
5110,5110,消耗金币490000,3,6,[490000],"[[11,10]]",5109
5111,5111,消耗金币500000,3,6,[500000],"[[11,10]]",5110
5112,5112,消耗金币510000,3,6,[510000],"[[11,10]]",5111
5113,5113,消耗金币520000,3,6,[520000],"[[11,10]]",5112
5114,5114,消耗金币530000,3,6,[530000],"[[11,10]]",5113
5115,5115,消耗金币540000,3,6,[540000],"[[11,10]]",5114
5116,5116,消耗金币550000,3,6,[550000],"[[11,10]]",5115
5117,5117,消耗金币560000,3,6,[560000],"[[11,10]]",5116
5118,5118,消耗金币570000,3,6,[570000],"[[11,10]]",5117
5119,5119,消耗金币580000,3,6,[580000],"[[11,10]]",5118
5120,5120,消耗金币590000,3,6,[590000],"[[11,10]]",5119
5121,5121,消耗金币600000,3,6,[600000],"[[11,10]]",5120
6000,6000,宝箱开启5次,3,11,[5],"[[11,10]]",
6001,6001,宝箱开启10次,3,11,[10],"[[11,10]]",6000
6002,6002,宝箱开启15次,3,11,[15],"[[11,10]]",6001
6003,6003,宝箱开启20次,3,11,[20],"[[11,10]]",6002
6004,6004,宝箱开启25次,3,11,[25],"[[11,10]]",6003
6005,6005,宝箱开启30次,3,11,[30],"[[11,10]]",6004
6006,6006,宝箱开启35次,3,11,[35],"[[11,10]]",6005
6007,6007,宝箱开启40次,3,11,[40],"[[11,10]]",6006
6008,6008,宝箱开启45次,3,11,[45],"[[11,10]]",6007
6009,6009,宝箱开启50次,3,11,[50],"[[11,10]]",6008
6010,6010,宝箱开启55次,3,11,[55],"[[11,10]]",6009
6011,6011,宝箱开启60次,3,11,[60],"[[11,10]]",6010
6012,6012,宝箱开启65次,3,11,[65],"[[11,10]]",6011
6013,6013,宝箱开启70次,3,11,[70],"[[11,10]]",6012
6014,6014,宝箱开启75次,3,11,[75],"[[11,10]]",6013
6015,6015,宝箱开启80次,3,11,[80],"[[11,10]]",6014
6016,6016,宝箱开启85次,3,11,[85],"[[11,10]]",6015
6017,6017,宝箱开启90次,3,11,[90],"[[11,10]]",6016
6018,6018,宝箱开启95次,3,11,[95],"[[11,10]]",6017
6019,6019,宝箱开启100次,3,11,[100],"[[11,10]]",6018
6020,6020,宝箱开启105次,3,11,[105],"[[11,10]]",6019
6021,6021,宝箱开启110次,3,11,[110],"[[11,10]]",6020
6022,6022,宝箱开启115次,3,11,[115],"[[11,10]]",6021
6023,6023,宝箱开启120次,3,11,[120],"[[11,10]]",6022
6024,6024,宝箱开启125次,3,11,[125],"[[11,10]]",6023
6025,6025,宝箱开启130次,3,11,[130],"[[11,10]]",6024
6026,6026,宝箱开启135次,3,11,[135],"[[11,10]]",6025
6027,6027,宝箱开启140次,3,11,[140],"[[11,10]]",6026
6028,6028,宝箱开启145次,3,11,[145],"[[11,10]]",6027
6029,6029,宝箱开启150次,3,11,[150],"[[11,10]]",6028
6030,6030,宝箱开启155次,3,11,[155],"[[11,10]]",6029
6031,6031,宝箱开启160次,3,11,[160],"[[11,10]]",6030
6032,6032,宝箱开启165次,3,11,[165],"[[11,10]]",6031
6033,6033,宝箱开启170次,3,11,[170],"[[11,10]]",6032
6034,6034,宝箱开启175次,3,11,[175],"[[11,10]]",6033
6035,6035,宝箱开启180次,3,11,[180],"[[11,10]]",6034
6036,6036,宝箱开启185次,3,11,[185],"[[11,10]]",6035
6037,6037,宝箱开启190次,3,11,[190],"[[11,10]]",6036
6038,6038,宝箱开启195次,3,11,[195],"[[11,10]]",6037
6039,6039,宝箱开启200次,3,11,[200],"[[11,10]]",6038
6040,6040,宝箱开启205次,3,11,[205],"[[11,10]]",6039
6041,6041,宝箱开启210次,3,11,[210],"[[11,10]]",6040
6042,6042,宝箱开启215次,3,11,[215],"[[11,10]]",6041
6043,6043,宝箱开启220次,3,11,[220],"[[11,10]]",6042
6044,6044,宝箱开启225次,3,11,[225],"[[11,10]]",6043
6045,6045,宝箱开启230次,3,11,[230],"[[11,10]]",6044
6046,6046,宝箱开启235次,3,11,[235],"[[11,10]]",6045
6047,6047,宝箱开启240次,3,11,[240],"[[11,10]]",6046
6048,6048,宝箱开启245次,3,11,[245],"[[11,10]]",6047
6049,6049,宝箱开启250次,3,11,[250],"[[11,10]]",6048
6050,6050,宝箱开启255次,3,11,[255],"[[11,10]]",6049
6051,6051,宝箱开启260次,3,11,[260],"[[11,10]]",6050
6052,6052,宝箱开启265次,3,11,[265],"[[11,10]]",6051
6053,6053,宝箱开启270次,3,11,[270],"[[11,10]]",6052
6054,6054,宝箱开启275次,3,11,[275],"[[11,10]]",6053
6055,6055,宝箱开启280次,3,11,[280],"[[11,10]]",6054
6056,6056,宝箱开启285次,3,11,[285],"[[11,10]]",6055
6057,6057,宝箱开启290次,3,11,[290],"[[11,10]]",6056
6058,6058,宝箱开启295次,3,11,[295],"[[11,10]]",6057
6059,6059,宝箱开启300次,3,11,[300],"[[11,10]]",6058
6060,6060,宝箱开启10次,3,11,[10],"[[11,10]]",
6061,6061,宝箱开启20次,3,11,[20],"[[11,10]]",6060
6062,6062,宝箱开启30次,3,11,[30],"[[11,10]]",6061
6063,6063,宝箱开启40次,3,11,[40],"[[11,10]]",6062
6064,6064,宝箱开启50次,3,11,[50],"[[11,10]]",6063
6065,6065,宝箱开启60次,3,11,[60],"[[11,10]]",6064
6066,6066,宝箱开启70次,3,11,[70],"[[11,10]]",6065
6067,6067,宝箱开启80次,3,11,[80],"[[11,10]]",6066
6068,6068,宝箱开启90次,3,11,[90],"[[11,10]]",6067
6069,6069,宝箱开启100次,3,11,[100],"[[11,10]]",6068
6070,6070,宝箱开启110次,3,11,[110],"[[11,10]]",6069
6071,6071,宝箱开启120次,3,11,[120],"[[11,10]]",6070
6072,6072,宝箱开启130次,3,11,[130],"[[11,10]]",6071
6073,6073,宝箱开启140次,3,11,[140],"[[11,10]]",6072
6074,6074,宝箱开启150次,3,11,[150],"[[11,10]]",6073
6075,6075,宝箱开启160次,3,11,[160],"[[11,10]]",6074
6076,6076,宝箱开启170次,3,11,[170],"[[11,10]]",6075
6077,6077,宝箱开启180次,3,11,[180],"[[11,10]]",6076
6078,6078,宝箱开启190次,3,11,[190],"[[11,10]]",6077
6079,6079,宝箱开启200次,3,11,[200],"[[11,10]]",6078
6080,6080,宝箱开启210次,3,11,[210],"[[11,10]]",6079
6081,6081,宝箱开启220次,3,11,[220],"[[11,10]]",6080
6082,6082,宝箱开启230次,3,11,[230],"[[11,10]]",6081
6083,6083,宝箱开启240次,3,11,[240],"[[11,10]]",6082
6084,6084,宝箱开启250次,3,11,[250],"[[11,10]]",6083
6085,6085,宝箱开启260次,3,11,[260],"[[11,10]]",6084
6086,6086,宝箱开启270次,3,11,[270],"[[11,10]]",6085
6087,6087,宝箱开启280次,3,11,[280],"[[11,10]]",6086
6088,6088,宝箱开启290次,3,11,[290],"[[11,10]]",6087
6089,6089,宝箱开启300次,3,11,[300],"[[11,10]]",6088
6090,6090,宝箱开启310次,3,11,[310],"[[11,10]]",6089
6091,6091,宝箱开启320次,3,11,[320],"[[11,10]]",6090
6092,6092,宝箱开启330次,3,11,[330],"[[11,10]]",6091
6093,6093,宝箱开启340次,3,11,[340],"[[11,10]]",6092
6094,6094,宝箱开启350次,3,11,[350],"[[11,10]]",6093
6095,6095,宝箱开启360次,3,11,[360],"[[11,10]]",6094
6096,6096,宝箱开启370次,3,11,[370],"[[11,10]]",6095
6097,6097,宝箱开启380次,3,11,[380],"[[11,10]]",6096
6098,6098,宝箱开启390次,3,11,[390],"[[11,10]]",6097
6099,6099,宝箱开启400次,3,11,[400],"[[11,10]]",6098
6100,6100,宝箱开启410次,3,11,[410],"[[11,10]]",6099
6101,6101,宝箱开启420次,3,11,[420],"[[11,10]]",6100
6102,6102,宝箱开启430次,3,11,[430],"[[11,10]]",6101
6103,6103,宝箱开启440次,3,11,[440],"[[11,10]]",6102
6104,6104,宝箱开启450次,3,11,[450],"[[11,10]]",6103
6105,6105,宝箱开启460次,3,11,[460],"[[11,10]]",6104
6106,6106,宝箱开启470次,3,11,[470],"[[11,10]]",6105
6107,6107,宝箱开启480次,3,11,[480],"[[11,10]]",6106
6108,6108,宝箱开启490次,3,11,[490],"[[11,10]]",6107
6109,6109,宝箱开启500次,3,11,[500],"[[11,10]]",6108
6110,6110,宝箱开启510次,3,11,[510],"[[11,10]]",6109
6111,6111,宝箱开启520次,3,11,[520],"[[11,10]]",6110
6112,6112,宝箱开启530次,3,11,[530],"[[11,10]]",6111
6113,6113,宝箱开启540次,3,11,[540],"[[11,10]]",6112
6114,6114,宝箱开启550次,3,11,[550],"[[11,10]]",6113
6115,6115,宝箱开启560次,3,11,[560],"[[11,10]]",6114
6116,6116,宝箱开启570次,3,11,[570],"[[11,10]]",6115
6117,6117,宝箱开启580次,3,11,[580],"[[11,10]]",6116
6118,6118,宝箱开启590次,3,11,[590],"[[11,10]]",6117
6119,6119,宝箱开启600次,3,11,[600],"[[11,10]]",6118
7000,7000,击杀1000只怪物,3,12,[1000],"[[11,10]]",
7001,7001,击杀2000只怪物,3,12,[2000],"[[11,10]]",7000
7002,7002,击杀3000只怪物,3,12,[3000],"[[11,10]]",7001
7003,7003,击杀4000只怪物,3,12,[4000],"[[11,10]]",7002
7004,7004,击杀5000只怪物,3,12,[5000],"[[11,10]]",7003
7005,7005,击杀6000只怪物,3,12,[6000],"[[11,10]]",7004
7006,7006,击杀7000只怪物,3,12,[7000],"[[11,10]]",7005
7007,7007,击杀8000只怪物,3,12,[8000],"[[11,10]]",7006
7008,7008,击杀9000只怪物,3,12,[9000],"[[11,10]]",7007
7009,7009,击杀10000只怪物,3,12,[10000],"[[11,10]]",7008
7010,7010,击杀11000只怪物,3,12,[11000],"[[11,10]]",7009
7011,7011,击杀12000只怪物,3,12,[12000],"[[11,10]]",7010
7012,7012,击杀13000只怪物,3,12,[13000],"[[11,10]]",7011
7013,7013,击杀14000只怪物,3,12,[14000],"[[11,10]]",7012
7014,7014,击杀15000只怪物,3,12,[15000],"[[11,10]]",7013
7015,7015,击杀16000只怪物,3,12,[16000],"[[11,10]]",7014
7016,7016,击杀17000只怪物,3,12,[17000],"[[11,10]]",7015
7017,7017,击杀18000只怪物,3,12,[18000],"[[11,10]]",7016
7018,7018,击杀19000只怪物,3,12,[19000],"[[11,10]]",7017
7019,7019,击杀20000只怪物,3,12,[20000],"[[11,10]]",7018
7020,7020,击杀21000只怪物,3,12,[21000],"[[11,10]]",7019
7021,7021,击杀22000只怪物,3,12,[22000],"[[11,10]]",7020
7022,7022,击杀23000只怪物,3,12,[23000],"[[11,10]]",7021
7023,7023,击杀24000只怪物,3,12,[24000],"[[11,10]]",7022
7024,7024,击杀25000只怪物,3,12,[25000],"[[11,10]]",7023
7025,7025,击杀26000只怪物,3,12,[26000],"[[11,10]]",7024
7026,7026,击杀27000只怪物,3,12,[27000],"[[11,10]]",7025
7027,7027,击杀28000只怪物,3,12,[28000],"[[11,10]]",7026
7028,7028,击杀29000只怪物,3,12,[29000],"[[11,10]]",7027
7029,7029,击杀30000只怪物,3,12,[30000],"[[11,10]]",7028
7030,7030,击杀31000只怪物,3,12,[31000],"[[11,10]]",7029
7031,7031,击杀32000只怪物,3,12,[32000],"[[11,10]]",7030
7032,7032,击杀33000只怪物,3,12,[33000],"[[11,10]]",7031
7033,7033,击杀34000只怪物,3,12,[34000],"[[11,10]]",7032
7034,7034,击杀35000只怪物,3,12,[35000],"[[11,10]]",7033
7035,7035,击杀36000只怪物,3,12,[36000],"[[11,10]]",7034
7036,7036,击杀37000只怪物,3,12,[37000],"[[11,10]]",7035
7037,7037,击杀38000只怪物,3,12,[38000],"[[11,10]]",7036
7038,7038,击杀39000只怪物,3,12,[39000],"[[11,10]]",7037
7039,7039,击杀40000只怪物,3,12,[40000],"[[11,10]]",7038
7040,7040,击杀41000只怪物,3,12,[41000],"[[11,10]]",7039
7041,7041,击杀42000只怪物,3,12,[42000],"[[11,10]]",7040
7042,7042,击杀43000只怪物,3,12,[43000],"[[11,10]]",7041
7043,7043,击杀44000只怪物,3,12,[44000],"[[11,10]]",7042
7044,7044,击杀45000只怪物,3,12,[45000],"[[11,10]]",7043
7045,7045,击杀46000只怪物,3,12,[46000],"[[11,10]]",7044
7046,7046,击杀47000只怪物,3,12,[47000],"[[11,10]]",7045
7047,7047,击杀48000只怪物,3,12,[48000],"[[11,10]]",7046
7048,7048,击杀49000只怪物,3,12,[49000],"[[11,10]]",7047
7049,7049,击杀50000只怪物,3,12,[50000],"[[11,10]]",7048
7050,7050,击杀51000只怪物,3,12,[51000],"[[11,10]]",7049
7051,7051,击杀52000只怪物,3,12,[52000],"[[11,10]]",7050
7052,7052,击杀53000只怪物,3,12,[53000],"[[11,10]]",7051
7053,7053,击杀54000只怪物,3,12,[54000],"[[11,10]]",7052
7054,7054,击杀55000只怪物,3,12,[55000],"[[11,10]]",7053
7055,7055,击杀56000只怪物,3,12,[56000],"[[11,10]]",7054
7056,7056,击杀57000只怪物,3,12,[57000],"[[11,10]]",7055
7057,7057,击杀58000只怪物,3,12,[58000],"[[11,10]]",7056
7058,7058,击杀59000只怪物,3,12,[59000],"[[11,10]]",7057
7059,7059,击杀60000只怪物,3,12,[60000],"[[11,10]]",7058
8000,8000,击杀1只BOSS,3,13,[1],"[[11,10]]",
8001,8001,击杀2只BOSS,3,13,[2],"[[11,10]]",8000
8002,8002,击杀3只BOSS,3,13,[3],"[[11,10]]",8001
8003,8003,击杀4只BOSS,3,13,[4],"[[11,10]]",8002
8004,8004,击杀5只BOSS,3,13,[5],"[[11,10]]",8003
8005,8005,击杀6只BOSS,3,13,[6],"[[11,10]]",8004
8006,8006,击杀7只BOSS,3,13,[7],"[[11,10]]",8005
8007,8007,击杀8只BOSS,3,13,[8],"[[11,10]]",8006
8008,8008,击杀9只BOSS,3,13,[9],"[[11,10]]",8007
8009,8009,击杀10只BOSS,3,13,[10],"[[11,10]]",8008
8010,8010,击杀11只BOSS,3,13,[11],"[[11,10]]",8009
8011,8011,击杀12只BOSS,3,13,[12],"[[11,10]]",8010
8012,8012,击杀13只BOSS,3,13,[13],"[[11,10]]",8011
8013,8013,击杀14只BOSS,3,13,[14],"[[11,10]]",8012
8014,8014,击杀15只BOSS,3,13,[15],"[[11,10]]",8013
8015,8015,击杀16只BOSS,3,13,[16],"[[11,10]]",8014
8016,8016,击杀17只BOSS,3,13,[17],"[[11,10]]",8015
8017,8017,击杀18只BOSS,3,13,[18],"[[11,10]]",8016
8018,8018,击杀19只BOSS,3,13,[19],"[[11,10]]",8017
8019,8019,击杀20只BOSS,3,13,[20],"[[11,10]]",8018
8020,8020,击杀21只BOSS,3,13,[21],"[[11,10]]",8019
8021,8021,击杀22只BOSS,3,13,[22],"[[11,10]]",8020
8022,8022,击杀23只BOSS,3,13,[23],"[[11,10]]",8021
8023,8023,击杀24只BOSS,3,13,[24],"[[11,10]]",8022
8024,8024,击杀25只BOSS,3,13,[25],"[[11,10]]",8023
8025,8025,击杀26只BOSS,3,13,[26],"[[11,10]]",8024
8026,8026,击杀27只BOSS,3,13,[27],"[[11,10]]",8025
8027,8027,击杀28只BOSS,3,13,[28],"[[11,10]]",8026
8028,8028,击杀29只BOSS,3,13,[29],"[[11,10]]",8027
8029,8029,击杀30只BOSS,3,13,[30],"[[11,10]]",8028
8030,8030,击杀31只BOSS,3,13,[31],"[[11,10]]",8029
8031,8031,击杀32只BOSS,3,13,[32],"[[11,10]]",8030
8032,8032,击杀33只BOSS,3,13,[33],"[[11,10]]",8031
8033,8033,击杀34只BOSS,3,13,[34],"[[11,10]]",8032
8034,8034,击杀35只BOSS,3,13,[35],"[[11,10]]",8033
8035,8035,击杀36只BOSS,3,13,[36],"[[11,10]]",8034
8036,8036,击杀37只BOSS,3,13,[37],"[[11,10]]",8035
8037,8037,击杀38只BOSS,3,13,[38],"[[11,10]]",8036
8038,8038,击杀39只BOSS,3,13,[39],"[[11,10]]",8037
8039,8039,击杀40只BOSS,3,13,[40],"[[11,10]]",8038
8040,8040,击杀41只BOSS,3,13,[41],"[[11,10]]",8039
8041,8041,击杀42只BOSS,3,13,[42],"[[11,10]]",8040
8042,8042,击杀43只BOSS,3,13,[43],"[[11,10]]",8041
8043,8043,击杀44只BOSS,3,13,[44],"[[11,10]]",8042
8044,8044,击杀45只BOSS,3,13,[45],"[[11,10]]",8043
8045,8045,击杀46只BOSS,3,13,[46],"[[11,10]]",8044
8046,8046,击杀47只BOSS,3,13,[47],"[[11,10]]",8045
8047,8047,击杀48只BOSS,3,13,[48],"[[11,10]]",8046
8048,8048,击杀49只BOSS,3,13,[49],"[[11,10]]",8047
8049,8049,击杀50只BOSS,3,13,[50],"[[11,10]]",8048
8050,8050,击杀51只BOSS,3,13,[51],"[[11,10]]",8049
8051,8051,击杀52只BOSS,3,13,[52],"[[11,10]]",8050
8052,8052,击杀53只BOSS,3,13,[53],"[[11,10]]",8051
8053,8053,击杀54只BOSS,3,13,[54],"[[11,10]]",8052
8054,8054,击杀55只BOSS,3,13,[55],"[[11,10]]",8053
8055,8055,击杀56只BOSS,3,13,[56],"[[11,10]]",8054
8056,8056,击杀57只BOSS,3,13,[57],"[[11,10]]",8055
8057,8057,击杀58只BOSS,3,13,[58],"[[11,10]]",8056
8058,8058,击杀59只BOSS,3,13,[59],"[[11,10]]",8057
8059,8059,击杀60只BOSS,3,13,[60],"[[11,10]]",8058
