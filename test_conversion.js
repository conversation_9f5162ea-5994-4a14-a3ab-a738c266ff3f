const fs = require('fs');
const path = require('path');
const { execSync } = require('child_process');

// 测试转换一致性的脚本
function testConversionConsistency() {
    console.log('开始测试JSON-CSV-JSON转换一致性...');
    
    // 备份原始JSON文件
    const originalJsonPath = './json/activity.json';
    const backupJsonPath = './json/activity_backup.json';
    
    if (fs.existsSync(originalJsonPath)) {
        fs.copyFileSync(originalJsonPath, backupJsonPath);
        console.log('已备份原始activity.json文件');
        
        // 读取原始JSON
        const originalData = JSON.parse(fs.readFileSync(originalJsonPath, 'utf8'));
        
        // 执行JSON转CSV
        console.log('执行JSON转CSV...');
        execSync('node jsonToCsv.js', { stdio: 'inherit' });
        
        // 执行CSV转JSON
        console.log('执行CSV转JSON...');
        execSync('node csvToJson.js', { stdio: 'inherit' });
        
        // 读取转换后的JSON
        const convertedData = JSON.parse(fs.readFileSync(originalJsonPath, 'utf8'));
        
        // 比较数据结构
        console.log('\n=== 数据一致性检查 ===');
        
        // 检查特定字段的类型
        const testId = '2';
        if (originalData[testId] && convertedData[testId]) {
            const original = originalData[testId];
            const converted = convertedData[testId];
            
            console.log('原始fightCount类型:', Array.isArray(original.fightCount) ? '数组' : typeof original.fightCount);
            console.log('转换后fightCount类型:', Array.isArray(converted.fightCount) ? '数组' : typeof converted.fightCount);
            console.log('fightCount值一致:', JSON.stringify(original.fightCount) === JSON.stringify(converted.fightCount));
            
            console.log('\n原始reward类型:', Array.isArray(original.reward) ? '数组' : typeof original.reward);
            console.log('转换后reward类型:', Array.isArray(converted.reward) ? '数组' : typeof converted.reward);
            console.log('reward值一致:', JSON.stringify(original.reward) === JSON.stringify(converted.reward));
            
            if (original.gameWinReward && converted.gameWinReward) {
                console.log('\n原始gameWinReward类型:', Array.isArray(original.gameWinReward) ? '数组' : typeof original.gameWinReward);
                console.log('转换后gameWinReward类型:', Array.isArray(converted.gameWinReward) ? '数组' : typeof converted.gameWinReward);
                console.log('gameWinReward值一致:', JSON.stringify(original.gameWinReward) === JSON.stringify(converted.gameWinReward));
            }
        }
        
        // 测试单元素数组（ID为3的reward字段）
        const testId3 = '3';
        if (originalData[testId3] && convertedData[testId3]) {
            const original3 = originalData[testId3];
            const converted3 = convertedData[testId3];
            
            console.log('\n=== 单元素数组测试 (ID:3) ===');
            console.log('原始reward类型:', Array.isArray(original3.reward) ? '数组' : typeof original3.reward);
            console.log('原始reward值:', JSON.stringify(original3.reward));
            console.log('转换后reward类型:', Array.isArray(converted3.reward) ? '数组' : typeof converted3.reward);
            console.log('转换后reward值:', JSON.stringify(converted3.reward));
            console.log('单元素数组一致性:', JSON.stringify(original3.reward) === JSON.stringify(converted3.reward));
        }
        
        // 整体数据比较
        const isIdentical = JSON.stringify(originalData, null, 2) === JSON.stringify(convertedData, null, 2);
        console.log('\n整体数据一致性:', isIdentical ? '✅ 完全一致' : '❌ 存在差异');
        
        if (!isIdentical) {
            console.log('\n详细差异分析:');
            // 这里可以添加更详细的差异分析逻辑
            Object.keys(originalData).forEach(key => {
                const originalItem = originalData[key];
                const convertedItem = convertedData[key];
                if (JSON.stringify(originalItem) !== JSON.stringify(convertedItem)) {
                    console.log(`ID ${key} 存在差异`);
                }
            });
        }
        
        // 恢复原始文件
        fs.copyFileSync(backupJsonPath, originalJsonPath);
        fs.unlinkSync(backupJsonPath);
        console.log('\n已恢复原始activity.json文件');
        
    } else {
        console.log('未找到activity.json文件');
    }
}

// 运行测试
testConversionConsistency();