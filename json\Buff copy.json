{"1": {"id": 1, "name": "�����˺�2", "rarity": 2, "desc": "<outline color=black width=2>�����˺�<color=#FFDD42>+100%</color></outline>", "type": 3, "time": -1, "isOverlay": 3, "icon": "v1/images/equip/item_20011", "attr": [100], "value": [[1]], "skillId": [6000, 60040, 60000]}, "2": {"id": 2, "name": "�����ٶ�", "rarity": 1, "desc": "<outline color=black width=2>���������ٶ�<color=#FFDD42>+50%</color></outline>", "type": 3, "time": -1, "isOverlay": 2, "icon": "v1/images/equip/item_20011", "attr": [133], "value": [[0.5]], "skillId": [6000, 60040, 60000]}, "3": {"id": 3, "name": "�ٶ�����2", "rarity": 2, "desc": "<outline color=black width=2>��������ٶ�<color=#FFDD42>+50%</color></outline>", "type": 3, "time": -1, "icon": "v1/images/equip/item_20011", "attr": [103], "value": [[-0.5]], "skillId": [6000, 60040]}, "4": {"id": 4, "name": "�������2", "rarity": 2, "desc": "<outline color=black width=2>��������<color=#FFDD42>+2</color></outline>", "type": 3, "time": -1, "isOverlay": 4, "icon": "v1/images/equip/item_20011", "attr": [101], "value": [[2]], "skillId": [6000, 60040]}, "5": {"id": 5, "name": "����1", "rarity": 2, "desc": "<outline color=black width=2>������������<color=#FFDD42>+1</color></outline>", "type": 3, "time": -1, "icon": "v1/images/equip/item_20011", "attr": [108], "value": [[1]], "skillId": [6000, 60040]}, "6": {"id": 6, "name": "����2", "rarity": 3, "desc": "<outline color=black width=2>������͸��<color=#FFDD42>+3</color></outline>", "type": 3, "time": -1, "isOverlay": 2, "icon": "v1/images/equip/item_20011", "attr": [120], "value": [[3]], "skillId": [6000, 60040, 60000]}, "7": {"id": 7, "name": "����", "rarity": 2, "desc": "<outline color=black width=2>�������к�ɷ���<color=#FFDD42>3ö</color>��͸����</outline>", "type": 3, "time": -1, "icon": "v1/images/equip/item_20011", "attr": [122], "value": [[60000, 5, 3, 0]], "skillId": [6000, 60040]}, "8": {"id": 8, "name": "��������", "rarity": 2, "desc": "<outline color=black width=2>�������к�ɷ��ѷ�����<color=#FFDD42>+1</color></outline>", "type": 3, "time": -1, "isOverlay": 5, "icon": "v1/images/equip/item_20011", "attr": [101], "value": [[1]], "skillId": [60000]}, "9": {"id": 9, "name": "�����˺�1", "rarity": 1, "desc": "<outline color=black width=2>�����˺�<color=#FFDD42>+30%</color></outline>", "type": 3, "time": -1, "isOverlay": 20, "icon": "v1/images/equip/item_20011", "attr": [100], "value": [[0.3]], "skillId": [6000, 60040, 60000]}, "10": {"id": 10, "name": "�ٶ�����1", "rarity": 1, "desc": "<outline color=black width=2>��������ٶ�<color=#FFDD42>+5%</color></outline>", "type": 3, "time": -1, "icon": "v1/images/equip/item_20011", "attr": [103], "value": [[-0.05]], "skillId": [6000, 60040]}, "11": {"id": 11, "name": "�������1", "rarity": 1, "desc": "<outline color=black width=2>��������<color=#FFDD42>+1</color></outline>", "type": 3, "time": -1, "isOverlay": 10, "icon": "v1/images/equip/item_20011", "attr": [101], "value": [[1]], "skillId": [6000, 60040]}, "12": {"id": 12, "name": "��������", "rarity": 2, "desc": "<outline color=black width=2>�������<color=#FFDD42>+100%</color>�˺�<color=#FFDD42>+30%</color></outline>", "type": 3, "time": -1, "isOverlay": 10, "icon": "v1/images/equip/item_20011", "attr": [102, 100], "value": [[1], [0.3]], "skillId": [6000, 60040, 60000]}, "13": {"id": 13, "name": "������", "rarity": 4, "desc": "<outline color=black width=2>ÿ�η������ʱ,�и���<color=#FFDD42>�Ƴ���ȴ</color>����<color=#FFDD42>3s</color></outline>", "type": 3, "time": -1, "icon": "v1/images/equip/item_20011", "attr": [143], "value": [[60020]], "skillId": [6000, 60040]}, "14": {"id": 14, "name": "����ר��", "rarity": 3, "desc": "<outline color=black width=2>���������ٶ�<color=#FFDD42>+50%</color>�˺�<color=#FFDD42>+100%</color></outline>", "type": 3, "time": -1, "icon": "v1/images/equip/item_20011", "attr": [133, 100], "value": [[0.5], [1]], "skillId": [6000, 60040, 60000]}, "15": {"id": 15, "name": "��������", "rarity": 3, "desc": "<outline color=black width=2>����������<color=#FFDD42>+10%</color>�˺�<color=#FFDD42>+50%</color></outline>", "type": 3, "time": -1, "isOverlay": 3, "icon": "v1/images/equip/item_20011", "attr": [148, 100], "value": [[0.1], [0.5]], "skillId": [6000, 60040, 60000]}, "16": {"id": 16, "name": "������ʦ", "rarity": 4, "desc": "<outline color=black width=2>�����˺�<color=#FFDD42>+1000%</color>������<color=#FFDD42>+5%</color>�����˺�������<color=#FFDD42>2��</color></outline>", "type": 3, "time": -1, "icon": "v1/images/equip/item_20011", "attr": [139], "value": [[60040]], "skillId": [6000]}, "17": {"id": 17, "name": "�������3", "rarity": 3, "desc": "<outline color=black width=2>��������<color=#FFDD42>+4</color></outline>", "type": 3, "time": -1, "isOverlay": 2, "icon": "v1/images/equip/item_20011", "attr": [101], "value": [[4]], "skillId": [6000, 60040]}, "18": {"id": 18, "name": "����2", "rarity": 3, "desc": "<outline color=black width=2>������������<color=#FFDD42>+2</color></outline>", "type": 3, "time": -1, "icon": "v1/images/equip/item_20011", "attr": [108], "value": [[2]], "skillId": [6000, 60040, 60000]}, "19": {"id": 19, "name": "��������", "rarity": 4, "desc": "<outline color=black width=2>�����滻Ϊ������,���к��и��ʼ���<color=#FFDD42>40%</color>����3s</outline>", "type": 3, "time": -1, "icon": "v1/images/equip/item_20011", "attr": [135, 109], "value": [[41], [190, 5, 3, 0]], "skillId": [6000, 60040, 60000]}, "20": {"id": 20, "name": "����", "rarity": 3, "desc": "<outline color=black width=2>���ӵ�Ϊ��������ʱ,�����и��ʶ���<color=#FFDD42>1s</color></outline>", "type": 3, "time": -1, "icon": "v1/images/equip/item_20011", "attr": [109], "value": [[200, 5, 3, 0]], "skillId": [6000, 60040, 60000]}, "21": {"id": 21, "name": "����1", "rarity": 2, "desc": "<outline color=black width=2>������͸��<color=#FFDD42>+1</color></outline>", "type": 3, "time": -1, "isOverlay": 3, "icon": "v1/images/equip/item_20011", "attr": [120], "value": [[1]], "skillId": [6000, 60040, 60000]}, "22": {"id": 22, "name": "���ͷ���", "rarity": 4, "desc": "<outline color=black width=2>�����˺�+2000%�������ķ����ٶ�<color=#FFDD42>����</color></outline>", "type": 3, "time": -1, "icon": "v1/images/equip/item_20011", "attr": [133, 100], "value": [[-0.9], [200]], "skillId": [6000, 60040, 60000]}, "23": {"id": 23, "name": "��������", "rarity": 4, "desc": "<outline color=black width=2>�������Է���<color=#FFDD42>1��</color>���ٶ�<color=#FFDD42>-40%</color></outline>", "type": 3, "time": -1, "icon": "v1/images/equip/item_20011", "attr": [500, 103], "value": [[2], [-0.4]], "skillId": [6000, 60040, 60000]}, "130": {"id": 130, "name": "������", "rarity": 4, "desc": "<outline color=black width=2>ÿ���ӵ����ʱ,�и���<color=#FFDD42>�Ƴ���ȴ</color>����<color=#FFDD42>3s</color></outline>", "type": 3, "time": 3, "isOverlay": 1, "icon": "img/ModeBulletsRebound/icon_fd_tltq", "attr": [103], "value": [[-1]], "skillId": [6000, 60040, 7000], "weight": 0.2, "res": ["bones/skill/fx_buff_ad", "2"]}, "190": {"id": 190, "name": "����", "rarity": 3, "desc": "<outline color=black width=2>�����ӵ�����<color=#FFDD42>25%</color>����2s</outline>", "type": 3, "time": 3, "icon": "v1/images/equip/item_20145", "attr": [8], "value": [[-0.4]], "weight": 0.2, "res": ["bones/skill/fx_deceleration", "2"], "ishideUI": 1}, "200": {"id": 200, "name": "��׶����", "rarity": 3, "desc": "<outline color=black width=2>����</outline>", "type": 2, "time": 3, "icon": "img/ModeBackpackHero/bufficon/bdqbd", "attr": [106], "value": [[0]], "weight": 0.15, "ishideUI": 1, "object": 1}, "1000": {"id": 1000, "name": "���ױ�", "rarity": 2, "desc": "<outline color=black width=2>������ױޣ�2����,ÿ�������˺���������<color=#FFDD42>10%</color>�����1���ض�<color=#FFDD42>����</color></outline>", "type": 3, "time": -1, "icon": "v1/images/equip/item_20034", "attr": [143], "value": [[6080]], "ishideUI": 1}, "1001": {"id": 1001, "name": "���ƽ�", "rarity": 2, "desc": "<outline color=black width=2>������ƽ�,����<color=#FFDD42>�ɵ��ӵ��ж�Ч��</color>,����3��</outline>", "type": 3, "time": -1, "icon": "v1/images/equip/item_20145", "attr": [143], "value": [[6100]], "ishideUI": 1}, "1002": {"id": 1002, "name": "߱��ǹ", "rarity": 2, "desc": "<outline color=black width=3>���߱��ǹ,���к���ѳ�<color=#FFDD42>3֧</color>��͸������ǹ</outline>", "type": 3, "time": -1, "icon": "v1/images/equip/item_20026", "attr": [143], "value": [[6120]], "ishideUI": 1}, "1003": {"id": 1003, "name": "�л��", "rarity": 2, "desc": "<outline color=black width=2>����л��,�����ͷ����<color=#FFDD42>150%</color>�˺����Ļ�</outline>", "type": 3, "time": -1, "icon": "v1/images/equip/item_20046", "attr": [143], "value": [[6140]], "ishideUI": 1}, "1004": {"id": 1004, "name": "��Ш", "rarity": 2, "desc": "<outline color=black width=3>�����Ш,ÿ�����е�λ�����ᴥ��<color=#FFDD42>����</color></outline>", "type": 3, "time": -1, "icon": "v1/images/equip/item_20153", "attr": [143], "value": [[6160]], "ishideUI": 1}, "1005": {"id": 1005, "name": "���콣", "rarity": 3, "desc": "<outline color=black width=3>������콣,�ͷź�,��ս�������ش���</outline>", "type": 3, "time": -1, "icon": "v1/images/equip/item_20143", "attr": [143], "value": [[6180]], "ishideUI": 1}, "1006": {"id": 1006, "name": "�캮�ؽ�", "rarity": 3, "desc": "<outline color=black width=3>����캮�ؽ�,������ʽΪ��ɨ�ҿɲ�������</outline>", "type": 3, "time": -1, "icon": "v1/images/equip/item_20065", "attr": [143], "value": [[6200]], "ishideUI": 1}, "1007": {"id": 1007, "name": "����ӡ", "rarity": 3, "desc": "<outline color=black width=3>��÷���ӡ,ӡ�����</outline>", "type": 3, "time": -1, "icon": "v1/images/equip/item_20164", "attr": [143], "value": [[6220]], "ishideUI": 1}, "1008": {"id": 1008, "name": "�Ͻ��«", "rarity": 3, "desc": "<outline color=black width=3>����Ͻ��«,�ų��ڶ����Ե�λ���<color=#FFDD42>���</color>�˺�</outline>", "type": 3, "time": -1, "icon": "v1/images/equip/item_20023", "attr": [143], "value": [[6240]], "ishideUI": 1}, "1009": {"id": 1009, "name": "��������", "rarity": 3, "desc": "<outline color=black width=3>��ö�������,Χ��������ת�һ���<color=#FFDD42>���</color></outline>", "type": 3, "time": -1, "icon": "v1/images/equip/item_20173", "attr": [143], "value": [[6260]], "ishideUI": 1}, "1010": {"id": 1010, "name": "������", "rarity": 3, "desc": "<outline color=black width=3>���������,�ٻ�������ֱ��ǰ<color=#FFDD42>����</color></outline>", "type": 3, "time": -1, "icon": "v1/images/equip/item_20045", "attr": [143], "value": [[6280]], "ishideUI": 1}, "1011": {"id": 1011, "name": "�����", "rarity": 2, "desc": "<outline color=black width=3>��÷����,�ͷź���𽥼��ٲ�ͣ���ڳ�����һ��ʱ��</outline>", "type": 3, "time": -1, "icon": "v1/images/equip/item_20025", "attr": [143], "value": [[6300]], "ishideUI": 1}, "1012": {"id": 1012, "name": "�𱬷�", "rarity": 1, "desc": "<outline color=black width=3>����𱬷�,��ը������<color=#FFDD42>4��</color>����</outline>", "type": 3, "time": -1, "icon": "v1/images/equip/item_20016", "attr": [143], "value": [[6320]], "ishideUI": 1}, "1013": {"id": 1013, "name": "������", "rarity": 1, "desc": "<outline color=black width=3>���������,����֮��<color=#FFDD42>��������</color>,��ɳ����˺�</outline>", "type": 3, "time": -1, "icon": "v1/images/equip/item_20103", "attr": [143], "value": [[6340]], "ishideUI": 1}, "1014": {"id": 1014, "name": "ȼ��ƿ", "rarity": 2, "desc": "<outline color=black width=3>���ȼ��ƿ</outline>", "type": 3, "time": -1, "icon": "v1/images/equip/icon_molotov_03", "attr": [143], "value": [[6360]], "ishideUI": 1}, "1015": {"id": 1015, "name": "�ɸ�", "rarity": 2, "desc": "<outline color=black width=3>��÷ɸ�</outline>", "type": 3, "time": -1, "icon": "v1/images/equip/icon_axe_03", "attr": [143], "value": [[6380]], "ishideUI": 1}, "1016": {"id": 1016, "name": "������", "rarity": 2, "desc": "<outline color=black width=3>��û�����</outline>", "type": 3, "time": -1, "icon": "v1/images/equip/icon_boomerang_03", "attr": [143], "value": [[6400]], "ishideUI": 1}, "1017": {"id": 1017, "name": "�ѻ�ǹ", "rarity": 2, "desc": "<outline color=black width=3>��þѻ�ǹ</outline>", "type": 3, "time": -1, "icon": "v1/images/equip/icon_sniperrifle_03", "attr": [143], "value": [[6420]], "ishideUI": 1}, "1018": {"id": 1018, "name": "ըҩ", "rarity": 2, "desc": "<outline color=black width=3>���ըҩ</outline>", "type": 3, "time": -1, "icon": "v1/images/equip/icon_dynamite_03", "attr": [143], "value": [[6440]], "ishideUI": 1}, "1019": {"id": 1019, "name": "����", "rarity": 2, "desc": "<outline color=black width=3>��õ���</outline>", "type": 3, "time": -1, "icon": "v1/images/equip/icon_Landmines3", "attr": [143], "value": [[6460]], "ishideUI": 1}, "1020": {"id": 1020, "name": "��ì", "rarity": 2, "desc": "<outline color=black width=3>��ó�ì</outline>", "type": 3, "time": -1, "icon": "v1/images/equip/icon_Spear3", "attr": [143], "value": [[6480]], "ishideUI": 1}, "1021": {"id": 1021, "name": "����", "rarity": 3, "desc": "<outline color=black width=3>��ù���</outline>", "type": 3, "time": -1, "icon": "v1/images/equip/icon_Stick3", "attr": [143], "value": [[6500]], "ishideUI": 1}, "1022": {"id": 1022, "name": "̫��", "rarity": 3, "desc": "<outline color=black width=3>���̫��</outline>", "type": 3, "time": -1, "icon": "v1/images/equip/icon_tachi3", "attr": [143], "value": [[6520]], "ishideUI": 1}, "1023": {"id": 1023, "name": "���Ϲ�", "rarity": 2, "desc": "<outline color=black width=3>��ø��Ϲ�</outline>", "type": 3, "time": -1, "icon": "v1/images/equip/icon_bow_03", "attr": [143], "value": [[6540]], "ishideUI": 1}, "1024": {"id": 1024, "name": "��������ƿ", "rarity": 3, "desc": "<outline color=black width=3>�����������ƿ,��ֱ���ϵĵ�λ����˺�</outline>", "type": 3, "time": -1, "icon": "v1/images/equip/item_20084", "attr": [143], "value": [[6620]], "ishideUI": 1}, "1025": {"id": 1025, "name": "ʯͷ��-����ǹ", "rarity": 4, "desc": "<b><outline color=black width=2>�������ǹ</outline></b></b>", "type": 4, "time": 0, "icon": "v1/images/equip/icon_shotgun", "attr": [143], "value": [[9060]], "ishideUI": 1}, "1026": {"id": 1026, "name": "ʯͷ��-����ǹ", "rarity": 4, "desc": "<b><outline color=black width=3>��ü���ǹ</outline></b>", "type": 4, "time": 0, "icon": "v1/images/equip/icon_dragonball", "attr": [143], "value": [[9040]], "ishideUI": 1}, "1027": {"id": 1027, "name": "ʯͷ��-��ǹ", "rarity": 4, "desc": "<b><outline color=black width=2>�����ǹ</outline></b>", "type": 4, "time": 0, "icon": "img/ModeBulletsRebound/icon_fd_tltq", "attr": [143], "value": [[9080]], "ishideUI": 1}, "1028": {"id": 1028, "name": "�ӵ�", "rarity": 4, "desc": "<outline color=black width=2>����ӵ�,������</outline>", "type": 3, "time": -1, "icon": "img/ModeBulletsRebound/icon_fd_tltq", "attr": [143], "value": [[6000]], "ishideUI": 1}, "1029": {"id": 1029, "name": "��м�", "rarity": 4, "desc": "<outline color=black width=2>�������</outline>", "type": 3, "time": -1, "icon": "img/ModeBulletsRebound/icon_fd_tltq", "attr": [143], "value": [[9100]], "ishideUI": 1}, "1033": {"id": 1033, "name": "ʯͷ��-��м�", "rarity": 4, "desc": "<b><outline color=black width=2>�����ϰ��</outline></b>", "type": 4, "time": 0, "icon": "img/ModeBulletsRebound/icon_fd_tltq", "attr": [143], "value": [[9100]], "ishideUI": 1}, "1034": {"id": 1034, "name": "������", "rarity": 4, "desc": "<outline color=black width=2>���������</outline>", "type": 3, "time": -1, "icon": "img/ModeBackpackHero/bufficon/skill_sdl", "attr": [143], "value": [[6840]], "ishideUI": 1}, "1035": {"id": 1035, "name": "��״����", "rarity": 4, "desc": "<outline color=black width=2>��ò�״����</outline>", "type": 3, "time": -1, "icon": "img/ModeBackpackHero/bufficon/skill_chzhshd", "attr": [143], "value": [[6860]], "ishideUI": 1}, "1036": {"id": 1036, "name": "���縫", "rarity": 4, "desc": "<outline color=black width=2>������縫ͷ,Χ��������ת</outline>", "type": 3, "time": -1, "icon": "img/ModeBackpackHero/bufficon/hatchet_skill", "attr": [143], "value": [[6880]], "ishideUI": 1}, "1037": {"id": 1037, "name": "��ʯ", "rarity": 4, "desc": "<outline color=black width=2>��ù�ʯ,��·���ϵĵ�λ����˺�</outline>", "type": 3, "time": -1, "icon": "img/ModeBackpackHero/bufficon/skill_gsh", "attr": [143], "value": [[6900]], "ishideUI": 1}, "1038": {"id": 1038, "name": "��ʯ", "rarity": 4, "desc": "<outline color=black width=2>�����ʯ,�������</outline>", "type": 3, "time": -1, "icon": "img/ModeBackpackHero/bufficon/skill_ysh", "attr": [143], "value": [[6920]], "ishideUI": 1}, "1039": {"id": 1039, "name": "����", "rarity": 4, "desc": "<outline color=black width=2>��ù���,�����鷢</outline>", "type": 3, "time": -1, "icon": "v1/images/equip/icon_arrow", "attr": [151], "value": [[30307]], "ishideUI": 1, "className": "<PERSON><PERSON>_<PERSON><PERSON>"}, "1041": {"id": 1041, "name": "ǹ��", "rarity": 4, "desc": "<outline color=black width=2>�������ǹ,�������</outline>", "type": 3, "time": -1, "icon": "v1/images/equip/item_20011", "attr": [151], "value": [[30308]], "ishideUI": 1, "className": "<PERSON><PERSON>_<PERSON><PERSON>"}, "1043": {"id": 1043, "name": "��ʦ", "rarity": 4, "desc": "<outline color=black width=2>��ʼ��û���,���ƿظ���Ԫ��֮��</outline>", "type": 3, "time": -1, "icon": "v1/images/equip/icon_Staff", "attr": [151], "value": [[30309]], "ishideUI": 1, "className": "<PERSON><PERSON>_<PERSON><PERSON>"}, "3000": {"id": 3000, "name": "���ױ�", "rarity": 4, "desc": "<outline color=black width=2>������ױޣ�2����,ÿ�������˺���������<color=#FFDD42>10%</color>�����1���ض�<color=#FFDD42>����</color></outline>", "type": 3, "time": -1, "icon": "v1/images/equip/item_20034", "attr": [143], "value": [[7080]], "ishideUI": 1}, "3001": {"id": 3001, "name": "���ƽ�", "rarity": 4, "desc": "<outline color=black width=2>������ƽ�,����<color=#FFDD42>�ɵ��ӵ��ж�Ч��</color>,����3��</outline>", "type": 3, "time": -1, "icon": "v1/images/equip/item_20145", "attr": [143], "value": [[7100]], "ishideUI": 1}, "3002": {"id": 3002, "name": "߱��ǹ", "rarity": 4, "desc": "<outline color=black width=3>���߱��ǹ,���к���ѳ�<color=#FFDD42>3֧</color>��͸������ǹ</outline>", "type": 3, "time": -1, "icon": "v1/images/equip/item_20026", "attr": [143], "value": [[7120]], "ishideUI": 1}, "3003": {"id": 3003, "name": "�л��", "rarity": 4, "desc": "<outline color=black width=2>����л��,�����ͷ����<color=#FFDD42>150%</color>�˺����Ļ�</outline>", "type": 3, "time": -1, "icon": "v1/images/equip/item_20046", "attr": [143], "value": [[7140]], "ishideUI": 1}, "3004": {"id": 3004, "name": "��Ш", "rarity": 4, "desc": "<outline color=black width=3>�����Ш,ÿ�����е�λ�����ᴥ��<color=#FFDD42>����</color></outline>", "type": 3, "time": -1, "icon": "v1/images/equip/item_20153", "attr": [143], "value": [[7160]], "ishideUI": 1}, "3005": {"id": 3005, "name": "���콣", "rarity": 4, "desc": "<outline color=black width=3>������콣,�ͷź�,��ս�������ش���</outline>", "type": 3, "time": -1, "icon": "v1/images/equip/item_20143", "attr": [143], "value": [[7180]], "ishideUI": 1}, "3006": {"id": 3006, "name": "�캮�ؽ�", "rarity": 4, "desc": "<outline color=black width=3>����캮�ؽ�,������ʽΪ��ɨ�ҿɲ�������</outline>", "type": 3, "time": -1, "icon": "v1/images/equip/item_20065", "attr": [143], "value": [[7200]], "ishideUI": 1}, "3007": {"id": 3007, "name": "����ӡ", "rarity": 4, "desc": "<outline color=black width=3>��÷���ӡ,ӡ�����</outline>", "type": 3, "time": -1, "icon": "v1/images/equip/item_20164", "attr": [143], "value": [[7220]], "ishideUI": 1}, "3008": {"id": 3008, "name": "�Ͻ��«", "rarity": 4, "desc": "<outline color=black width=3>����Ͻ��«,�ų��ڶ����Ե�λ���<color=#FFDD42>���</color>�˺�</outline>", "type": 3, "time": -1, "icon": "v1/images/equip/item_20023", "attr": [143], "value": [[7240]], "ishideUI": 1}, "3009": {"id": 3009, "name": "��������", "rarity": 4, "desc": "<outline color=black width=3>��ö�������,Χ��������ת�һ���<color=#FFDD42>���</color></outline>", "type": 3, "time": -1, "icon": "v1/images/equip/item_20173", "attr": [143], "value": [[7260]], "ishideUI": 1}, "3010": {"id": 3010, "name": "������", "rarity": 4, "desc": "<outline color=black width=3>���������,�ٻ�������ֱ��ǰ<color=#FFDD42����</color></outline>", "type": 3, "time": -1, "icon": "v1/images/equip/item_20045", "attr": [143], "value": [[7280]], "ishideUI": 1}, "3011": {"id": 3011, "name": "�����", "rarity": 4, "desc": "<outline color=black width=3>��÷����,�ͷź���𽥼��ٲ�ͣ���ڳ�����һ��ʱ��</outline>", "type": 3, "time": -1, "icon": "v1/images/equip/item_20025", "attr": [143], "value": [[7300]], "ishideUI": 1}, "3012": {"id": 3012, "name": "�𱬷�", "rarity": 4, "desc": "<outline color=black width=3>����𱬷�,��ը������<color=#FFDD42>4��</color>����</outline>", "type": 3, "time": -1, "icon": "v1/images/equip/item_20016", "attr": [143], "value": [[7320]], "ishideUI": 1}, "3013": {"id": 3013, "name": "������", "rarity": 4, "desc": "<outline color=black width=3>���������,����֮��<color=#FFDD42>��������</color>,��ɳ����˺�</outline>", "type": 3, "time": -1, "icon": "v1/images/equip/item_20103", "attr": [143], "value": [[7340]], "ishideUI": 1}, "3014": {"id": 3014, "name": "ȼ��ƿ", "rarity": 2, "desc": "<outline color=black width=3>���ȼ��ƿ</outline>", "type": 3, "time": -1, "icon": "v1/images/equip/icon_molotov_03", "attr": [143], "value": [[7360]], "ishideUI": 1}, "3015": {"id": 3015, "name": "�ɸ�", "rarity": 2, "desc": "<outline color=black width=3>��÷ɸ�</outline>", "type": 3, "time": -1, "icon": "v1/images/equip/icon_axe_03", "attr": [143], "value": [[7380]], "ishideUI": 1}, "3016": {"id": 3016, "name": "������", "rarity": 2, "desc": "<outline color=black width=3>��û�����</outline>", "type": 3, "time": -1, "icon": "v1/images/equip/icon_boomerang_03", "attr": [143], "value": [[7400]], "ishideUI": 1}, "3017": {"id": 3017, "name": "�ѻ�ǹ", "rarity": 2, "desc": "<outline color=black width=3>��þѻ�ǹ</outline>", "type": 3, "time": -1, "icon": "v1/images/equip/icon_sniperrifle_03", "attr": [143], "value": [[7420]], "ishideUI": 1}, "3018": {"id": 3018, "name": "ըҩ", "rarity": 2, "desc": "<outline color=black width=3>���ըҩ</outline>", "type": 3, "time": -1, "icon": "v1/images/equip/icon_dynamite_03", "attr": [143], "value": [[7440]], "ishideUI": 1}, "3019": {"id": 3019, "name": "����", "rarity": 2, "desc": "<outline color=black width=3>��õ���</outline>", "type": 3, "time": -1, "icon": "v1/images/equip/icon_Landmines3", "attr": [143], "value": [[7460]], "ishideUI": 1}, "3020": {"id": 3020, "name": "��ì", "rarity": 2, "desc": "<outline color=black width=3>��ó�ì</outline>", "type": 3, "time": -1, "icon": "v1/images/equip/icon_Spear3", "attr": [143], "value": [[7480]], "ishideUI": 1}, "3021": {"id": 3021, "name": "����", "rarity": 3, "desc": "<outline color=black width=3>��ù���</outline>", "type": 3, "time": -1, "icon": "v1/images/equip/icon_Stick3", "attr": [143], "value": [[7500]], "ishideUI": 1}, "3022": {"id": 3022, "name": "̫��", "rarity": 3, "desc": "<outline color=black width=3>���̫��</outline>", "type": 3, "time": -1, "icon": "v1/images/equip/icon_tachi3", "attr": [143], "value": [[7520]], "ishideUI": 1}, "3023": {"id": 3023, "name": "���Ϲ�", "rarity": 2, "desc": "<outline color=black width=3>��ø��Ϲ�</outline>", "type": 3, "time": -1, "icon": "v1/images/equip/icon_bow_03", "attr": [143], "value": [[7540]], "ishideUI": 1}, "4000": {"id": 4000, "name": "�˺�", "rarity": 1, "desc": "<outline color=black width=2>�ӵ��˺�<color=#FFDD42>+100%</color></outline>", "type": 3, "time": -1, "isOverlay": 99999, "icon": "img/ModeBulletsRebound/icon_fd_tltq", "attr": [100], "value": [[]], "skillId": [9020]}, "4001": {"id": 4001, "name": "����", "rarity": 1, "desc": "<outline color=black width=2>�ӵ������ٶ�<color=#FFDD42>+100%</color></outline>", "type": 3, "time": -1, "isOverlay": 99999, "icon": "img/ModeBulletsRebound/icon_fd_tltq", "attr": [103], "value": [[]], "skillId": [9020]}, "4002": {"id": 4002, "name": "����", "rarity": 2, "desc": "<outline color=black width=2>�ӵ�����<color=#FFDD42>+3</color></outline>", "type": 3, "time": -1, "isOverlay": 99999, "icon": "img/ModeBulletsRebound/icon_fd_tltq", "attr": [101], "value": [[]], "skillId": [9020]}, "8000": {"id": 8000, "name": "ȼ��ƿ����", "rarity": 2, "type": 6, "time": 5, "attr": [124], "value": [[1]], "res": ["bones/skill/fx_burn", "2"], "ishideUI": 1, "object": 1, "className": "B<PERSON>_<PERSON>", "otherValue": [0.5]}, "9062": {"id": 9062, "name": "����boss�ٻ�ʯ��", "desc": "<outline color=black width=2></outline>", "type": 1, "time": -1, "attr": [300], "value": [[6, 30283]], "ishideUI": 1, "otherValue": [9063]}, "9063": {"id": 9063, "name": "�ٻ���λ����buff", "desc": "<outline color=black width=2>��������95%��������200%</outline>", "type": 2, "time": -1, "attr": [1, 8], "value": [[-0.97], [2.5]], "ishideUI": 1}, "9993": {"id": 9993, "name": "��ͷ��ʼ����", "rarity": 4, "desc": "<outline color=black width=2>����+200%</outline>", "type": 1, "time": 4.5, "attr": [8], "value": [[4]], "ishideUI": 1}, "9994": {"id": 9994, "name": "�����޵�", "rarity": 4, "desc": "<outline color=black width=2>�����˺�</outline>", "type": 1, "time": 5, "attr": [12], "value": [[0]], "res": ["bones/skill/fx_invincible", "2"], "ishideUI": 1}, "9995": {"id": 9995, "name": "��ͷ��ʼ����", "rarity": 4, "desc": "<outline color=black width=2>����+200%</outline>", "type": 1, "time": 5, "attr": [8], "value": [[6]], "ishideUI": 1}, "9996": {"id": 9996, "name": "��ͷ��ʼ����", "rarity": 4, "desc": "<outline color=black width=2>����+200%</outline>", "type": 1, "time": 12, "attr": [8], "value": [[6]], "ishideUI": 1}, "9997": {"id": 9997, "name": "�����ܼ���", "rarity": 3, "desc": "<outline color=black width=2>����10%</outline>", "type": 1, "time": 3, "attr": [8], "value": [[1]], "weight": 0.4, "ishideUI": 1}, "9998": {"id": 9998, "name": "��ͷ��ʼ����", "rarity": 4, "desc": "<outline color=black width=2>����+200%</outline>", "type": 1, "time": 11, "attr": [8], "value": [[6]], "ishideUI": 1}, "9999": {"id": 9999, "name": "��ͷ��ʼ����5��", "rarity": 4, "desc": "<outline color=black width=2>�����˺�</outline>", "type": 1, "time": 5.5, "attr": [8], "value": [[2]], "ishideUI": 1}, "10000": {"id": 10000, "name": "�˺�����1", "rarity": 2, "desc": "<outline color=black width=2>���ױ��˺�<color=#FFDD42>+50%</color></outline>", "type": 3, "time": -1, "isOverlay": 10, "icon": "v1/images/equip/item_20034", "attr": [100], "value": [[0.5]], "skillId": [6080], "ishideUI": 1}, "10001": {"id": 10001, "name": "�ٶ�����", "rarity": 2, "desc": "<outline color=black width=2>���ױ��ٶ�<color=#FFDD42>+25%</color></outline>", "type": 3, "time": -1, "isOverlay": 2, "icon": "v1/images/equip/item_20034", "attr": [103], "value": [[-0.25]], "skillId": [6080], "ishideUI": 1}, "10002": {"id": 10002, "name": "�����ӳ�", "rarity": 2, "desc": "<outline color=black width=2>���ױ޹�����Χ<color=#FFDD42>+25%</color></outline>", "type": 3, "time": -1, "isOverlay": 4, "icon": "v1/images/equip/item_20034", "attr": [102, 110], "value": [[0.25], [0.12]], "skillId": [6080], "ishideUI": 1}, "10003": {"id": 10003, "name": "����4��", "rarity": 4, "desc": "<outline color=black width=2>���ױ�����<color=#FFDD42>+4</color></outline>", "type": 3, "time": -1, "icon": "v1/images/equip/item_20034", "attr": [101], "value": [[4]], "skillId": [6080], "ishideUI": 1}, "10004": {"id": 10004, "name": "����֮��", "rarity": 3, "desc": "<outline color=black width=2>����֮�и�������Ч�������е��˻�<color=#FFDD42>����</color></outline>", "type": 3, "time": -1, "icon": "img/ModeBackpackHero/bufficon/hdzy", "attr": [135, 109], "value": [[7], [100050, 5, 3, 0]], "skillId": [6080]}, "10005": {"id": 10005, "name": "����֮��", "rarity": 4, "desc": "<outline color=black width=2>���ױޱޱ�<color=#FFDD42>����</color></outline>", "type": 3, "time": -1, "icon": "v1/images/equip/item_20034", "attr": [148], "value": [[1]], "skillId": [6080], "ishideUI": 1}, "10006": {"id": 10006, "name": "����һ��", "rarity": 4, "desc": "<outline color=black width=2>���ױޱ����˺�<color=#FFDD42>+200%</color></outline>", "type": 3, "time": -1, "icon": "v1/images/equip/item_20034", "attr": [149], "value": [[1]], "skillId": [6080], "ishideUI": 1}, "10007": {"id": 10007, "name": "����", "rarity": 3, "desc": "<outline color=black width=2>���ױ����к󸽴�����Ч��,ʹ���ܵ����˺�<color=#FFDD42>+20%</color>����<color=#FFDD42>+3s</color>�ɵ���3��</outline>", "type": 3, "time": -1, "icon": "v1/images/equip/item_20034", "attr": [109], "value": [[100070, 5, 3, 0]], "skillId": [6080], "ishideUI": 1}, "10008": {"id": 10008, "name": "�˺�����2", "rarity": 3, "desc": "<outline color=black width=2>���ױ��˺�<color=#FFDD42>+200%</color></outline>", "type": 3, "time": -1, "isOverlay": 3, "icon": "v1/images/equip/item_20034", "attr": [100], "value": [[2]], "skillId": [6080], "ishideUI": 1}, "10010": {"id": 10010, "name": "˫��", "rarity": 3, "desc": "<outline color=black width=2>�㶾֮������<color=#FFDD42>+2</color></outline>", "type": 3, "time": -1, "isOverlay": 2, "icon": "img/ModeBackpackHero/bufficon/cdsr", "attr": [101], "value": [[2]], "skillId": [6100]}, "10011": {"id": 10011, "name": "�ٶ�����", "rarity": 2, "desc": "<outline color=black width=2>���ƽ��˺�<color=#FFDD42>+50%</color></outline>", "type": 3, "time": -1, "isOverlay": 6, "icon": "v1/images/equip/item_20145", "attr": [100], "value": [[0.5]], "skillId": [6100], "ishideUI": 1}, "10012": {"id": 10012, "name": "�ٶ�����", "rarity": 2, "desc": "<outline color=black width=2>���ƽ��ٶ�<color=#FFDD42>+25%</color></outline>", "type": 3, "time": -1, "isOverlay": 2, "icon": "v1/images/equip/item_20145", "attr": [103], "value": [[-0.25]], "skillId": [6100], "ishideUI": 1}, "10013": {"id": 10013, "name": "�綾֮��", "rarity": 4, "desc": "<outline color=black width=2>���ƽ����к����ڵ��ϲ�����Һ�Դ����ĵ�λ���<color=#FFDD42>�����˺�</color></outline>", "type": 3, "time": -1, "icon": "v1/images/equip/item_20145", "attr": [122], "value": [[61000, 5, 3, 0]], "skillId": [6100], "ishideUI": 1}, "10014": {"id": 10014, "name": "����", "rarity": 3, "desc": "<outline color=black width=2>���ƽ�������<color=#FFDD42>+25%</color></outline>", "type": 3, "time": -1, "icon": "v1/images/equip/item_20145", "attr": [148], "value": [[0.25]], "skillId": [6100], "ishideUI": 1}, "10015": {"id": 10015, "name": "��Һ����", "rarity": 4, "desc": "<outline color=black width=2>���ƽ��ж��˺�<color=#FFDD42>+100%</color>���ж�Ч����������<color=#FFDD42>��λ����</color></outline>", "type": 1, "time": -1, "icon": "v1/images/equip/item_20145", "attr": [147, 145], "value": [[1], [999]], "skillId": [6100], "ishideUI": 1, "buffId": [100100]}, "10016": {"id": 10016, "name": "����", "rarity": 3, "desc": "<outline color=black width=2>���ƽ��ж����Ӳ���<color=#FFDD42>+5</color></outline>", "type": 1, "time": -1, "isOverlay": 2, "icon": "v1/images/equip/item_20145", "attr": [129], "value": [[5]], "skillId": [6100], "ishideUI": 1, "buffId": [100100]}, "10017": {"id": 10017, "name": "�ٶ�", "rarity": 3, "desc": "<outline color=black width=2>���ƽ����к����ʹ��λ����<color=#FFDD42>10%</color>����3s�ɵ���4��</outline>", "type": 3, "time": -1, "icon": "v1/images/equip/item_20145", "attr": [109], "value": [[100170, 5, 3, 0]], "skillId": [6100], "ishideUI": 1}, "10018": {"id": 10018, "name": "��Һר��", "rarity": 3, "desc": "<outline color=black width=2>���ƽ��ж��˺�<color=#FFDD42>+25%</color>�����˺����<color=#FFDD42>-25%</color></outline>", "type": 3, "time": -1, "isOverlay": 2, "icon": "v1/images/equip/item_20145", "attr": [147, 150], "value": [[0.25], [-0.25]], "skillId": [6100], "ishideUI": 1, "buffId": [100100]}, "10019": {"id": 10019, "name": "����ʹͽ", "rarity": 4, "desc": "<outline color=black width=2>�㶾֮���˺�<color=#FFDD42>+300%</color>����<color=#FFDD42>+4</color></outline>", "type": 3, "time": -1, "icon": "img/ModeBackpackHero/bufficon/cdsr", "attr": [100, 101], "value": [[3], [4]], "skillId": [6100]}, "10020": {"id": 10020, "name": "�˺�����1", "rarity": 2, "desc": "<outline color=black width=2>߱��ǹ�˺�<color=#FFDD42>+50%</color></outline>", "type": 3, "time": -1, "isOverlay": 10, "icon": "v1/images/equip/item_20026", "attr": [100], "value": [[0.5]], "skillId": [6120, 61240, 61260, 61200, 61220], "ishideUI": 1}, "10021": {"id": 10021, "name": "����Ͷ��", "rarity": 2, "desc": "<outline color=black width=2>߱��ǹ�ٶ�<color=#FFDD42>+25%</color></outline>", "type": 3, "time": -1, "isOverlay": 2, "icon": "v1/images/equip/item_20026", "attr": [103], "value": [[-0.25]], "skillId": [6120, 61240, 61260, 61200, 61220], "ishideUI": 1}, "10022": {"id": 10022, "name": "߱��һ��", "rarity": 4, "desc": "<outline color=black width=2>߱��ǹ���е�λ������<color=#FFDD42>����ը</color></outline>", "type": 3, "time": -1, "icon": "v1/images/equip/item_20026", "attr": [139], "value": [[61200]], "skillId": [6120], "ishideUI": 1}, "10023": {"id": 10023, "name": "����", "rarity": 2, "desc": "<outline color=black width=2>߱��ǹ���ѵ�ǹ����<color=#FFDD42>+1</color></outline>", "type": 3, "time": -1, "isOverlay": 4, "icon": "v1/images/equip/item_20026", "attr": [101], "value": [[1]], "skillId": [61260], "ishideUI": 1}, "10024": {"id": 10024, "name": "����", "rarity": 3, "desc": "<outline color=black width=2>߱��ǹ����ǹ��<color=#FFDD42>+3</color></outline>", "type": 3, "time": -1, "isOverlay": 2, "icon": "v1/images/equip/item_20026", "attr": [101], "value": [[2]], "skillId": [6120], "ishideUI": 1}, "10025": {"id": 10025, "name": "���޵���", "rarity": 3, "desc": "<outline color=black width=2>߱��ǹ���ѵ�ǹ�����ٶ�<color=#FFDD42>+80%</color>��������<color=#FFDD42>+1</color></outline>", "type": 3, "time": -1, "icon": "v1/images/equip/item_20026", "attr": [133, 500], "value": [[0.8], [1]], "skillId": [61260], "ishideUI": 1}, "10026": {"id": 10026, "name": "����", "rarity": 3, "desc": "<outline color=black width=2>߱��ǹ������<color=#FFDD42>+15%</color></outline>", "type": 3, "time": -1, "isOverlay": 3, "icon": "v1/images/equip/item_20026", "attr": [148], "value": [[0.15]], "skillId": [6120, 61240, 61260, 61200, 61220], "ishideUI": 1}, "10027": {"id": 10027, "name": "߱��͸", "rarity": 4, "desc": "<outline color=black width=2>߱��ǹ�˺�<color=#FFDD42>+150%</color>,��͸����<color=#FFDD42>+2</color>(�ɵ���3��)</outline>", "type": 3, "time": -1, "icon": "v1/images/equip/item_20026", "attr": [100, 120], "value": [[1.5], [2]], "skillId": [6120, 61200], "ishideUI": 1}, "10028": {"id": 10028, "name": "�˺�����2", "rarity": 3, "desc": "<outline color=black width=2>߱��ǹ�˺�<color=#FFDD42>+100%</color></outline>", "type": 3, "time": -1, "isOverlay": 3, "icon": "v1/images/equip/item_20026", "attr": [100], "value": [[1]], "skillId": [6120, 61240, 61260, 61200, 61220], "ishideUI": 1}, "10029": {"id": 10029, "name": "����", "rarity": 3, "desc": "<outline color=black width=2>߱��ǹ�����˺�<color=#FFDD42>+25%</color></outline>", "type": 3, "time": -1, "isOverlay": 4, "icon": "v1/images/equip/item_20026", "attr": [149], "value": [[0.25]], "skillId": [6120, 61240, 61260, 61200, 61220], "ishideUI": 1}, "10030": {"id": 10030, "name": "�˺�����1", "rarity": 2, "desc": "<outline color=black width=2>�л���˺�<color=#FFDD42>+50%</color></outline>", "type": 3, "time": -1, "isOverlay": 10, "icon": "v1/images/equip/item_20046", "attr": [100], "value": [[0.5]], "skillId": [6140, 61400], "ishideUI": 1}, "10031": {"id": 10031, "name": "���", "rarity": 2, "desc": "<outline color=black width=2>�Ļ����<color=#FFDD42>+25%</color></outline>", "type": 3, "time": -1, "isOverlay": 2, "icon": "v1/images/equip/item_20046", "attr": [102], "value": [[0.25]], "skillId": [6140, 61400], "ishideUI": 1}, "10032": {"id": 10032, "name": "�Ļ�����", "rarity": 4, "desc": "<outline color=black width=2>�Ļ���<color=#FFDD42>���Ļ�</color>���ƶ���ʽ�ı䣬�˺�+200%�����е��˻����<color=#FFDD42>��������</color></outline>", "type": 3, "time": -1, "icon": "v1/images/equip/item_20046", "attr": [100, 109, 135], "value": [[2], [100320, 5, 3, 0], [351]], "skillId": [6140], "ishideUI": 1}, "10033": {"id": 10033, "name": "Ѹ��", "rarity": 2, "desc": "<outline color=black width=2>�Ļ��ٶ�<color=#FFDD42>+25%</color></outline>", "type": 3, "time": -1, "isOverlay": 2, "icon": "v1/images/equip/item_20046", "attr": [103], "value": [[-0.25]], "skillId": [6140], "ishideUI": 1}, "10034": {"id": 10034, "name": "����", "rarity": 3, "desc": "<outline color=black width=2>�Ļ걩����<color=#FFDD42>+25%</color></outline>", "type": 3, "time": -1, "icon": "v1/images/equip/item_20046", "attr": [148], "value": [[0.25]], "skillId": [6140, 61400]}, "10035": {"id": 10035, "name": "����", "rarity": 3, "desc": "<outline color=black width=2>�Ļ걩���˺�<color=#FFDD42>+50%</color></outline>", "type": 3, "time": -1, "isOverlay": 2, "icon": "v1/images/equip/item_20046", "attr": [149], "value": [[0.5]], "skillId": [6140, 61400], "ishideUI": 1}, "10036": {"id": 10036, "name": "�˺�����2", "rarity": 3, "desc": "<outline color=black width=2>�Ļ��˺�<color=#FFDD42>+100%</color></outline>", "type": 3, "time": -1, "isOverlay": 3, "icon": "v1/images/equip/item_20046", "attr": [100], "value": [[1]], "skillId": [6140], "ishideUI": 1}, "10037": {"id": 10037, "name": "���Ľ���", "rarity": 4, "desc": "<outline color=black width=2>�л���ͷ�<color=#FFDD42>9��</color>�Ļ�</outline>", "type": 3, "time": -1, "icon": "v1/images/equip/item_20046", "attr": [101], "value": [[8]], "skillId": [6140], "ishideUI": 1}, "10038": {"id": 10038, "name": "�Ļ�֮��", "rarity": 4, "desc": "<outline color=black width=2>�Ļ����<color=#FFDD42>+100%</color>�˺�<color=#FFDD42>+1000%</color></outline>", "type": 3, "time": -1, "icon": "v1/images/equip/item_20046", "attr": [102, 100], "value": [[1], [10]], "skillId": [6140], "ishideUI": 1}, "10039": {"id": 10039, "name": "����", "rarity": 3, "desc": "<outline color=black width=2>�Ļ����к�����<color=#FFDD42>1��</color>�Ļ�</outline>", "type": 3, "time": -1, "icon": "v1/images/equip/item_20046", "attr": [122], "value": [[61400, 5, 3, 0.1]], "skillId": [6140], "ishideUI": 1}, "10040": {"id": 10040, "name": "�׵�޸�", "rarity": 2, "desc": "<outline color=black width=2>��Ш���<color=#FFDD42>+20%</color></outline>", "type": 3, "time": -1, "isOverlay": 2, "icon": "v1/images/equip/item_20153", "attr": [102], "value": [[0.2]], "skillId": [6160, 61660], "ishideUI": 1}, "10041": {"id": 10041, "name": "����", "rarity": 3, "desc": "<outline color=black width=2>��Ш���е�λʱ�������λ���ͷ�<color=#FFDD42>3��</color>����</outline>", "type": 3, "time": -1, "icon": "v1/images/equip/item_20153", "attr": [122], "value": [[61620, 5, 3, 0.2]], "skillId": [6160, 61660], "ishideUI": 1}, "10042": {"id": 10042, "name": "����", "rarity": 2, "desc": "<outline color=black width=2>��Ш�ٶ�<color=#FFDD42>+25%</color></outline>", "type": 3, "time": -1, "isOverlay": 2, "icon": "v1/images/equip/item_20153", "attr": [103], "value": [[-0.25]], "skillId": [6160], "ishideUI": 1}, "10043": {"id": 10043, "name": "�������", "rarity": 2, "desc": "<outline color=black width=2>��Ш�����������˺�<color=#FFDD42>+100%</color></outline>", "type": 3, "time": -1, "isOverlay": 5, "icon": "v1/images/equip/item_20153", "attr": [100], "value": [[1]], "skillId": [61600, 61620], "ishideUI": 1}, "10044": {"id": 10044, "name": "�ƿ�", "rarity": 3, "desc": "<outline color=black width=2>��Ш�ɳ���,��<color=#FFDD42>ͣ��</color>һ��ʱ��</outline>", "type": 3, "time": -1, "icon": "v1/images/equip/item_20153", "attr": [122], "value": [[61660, 4, 2, 0]], "skillId": [6160], "ishideUI": 1}, "10045": {"id": 10045, "name": "�������", "rarity": 4, "desc": "<outline color=black width=2>��Ш���к�����<color=#FFDD42>�������</color></outline>", "type": 3, "time": -1, "icon": "v1/images/equip/item_20153", "attr": [122], "value": [[61640, 5, 3, 0]], "skillId": [6160], "ishideUI": 1}, "10046": {"id": 10046, "name": "����", "rarity": 3, "desc": "<outline color=black width=2>��Ш������<color=#FFDD42>+15%</color></outline>", "type": 3, "time": -1, "isOverlay": 3, "icon": "v1/images/equip/item_20153", "attr": [148], "value": [[0.15]], "skillId": [6160, 61600, 61620, 61640, 61660], "ishideUI": 1}, "10047": {"id": 10047, "name": "����", "rarity": 3, "desc": "<outline color=black width=2>��Ш�����˺�<color=#FFDD42>+25%</color></outline>", "type": 3, "time": -1, "isOverlay": 4, "icon": "v1/images/equip/item_20153", "attr": [149], "value": [[0.25]], "skillId": [6160, 61600, 61620, 61640, 61660], "ishideUI": 1}, "10048": {"id": 10048, "name": "�˺�����1", "rarity": 2, "desc": "<outline color=black width=2>��Ш�˺�<color=#FFDD42>+50%</color></outline>", "type": 3, "time": -1, "isOverlay": 10, "icon": "v1/images/equip/item_20153", "attr": [100], "value": [[0.5]], "skillId": [6160, 61660], "ishideUI": 1}, "10049": {"id": 10049, "name": "�˺�����2", "rarity": 3, "desc": "<outline color=black width=2>��Ш�˺�<color=#FFDD42>+200%</color></outline>", "type": 3, "time": -1, "isOverlay": 3, "icon": "v1/images/equip/item_20153", "attr": [100], "value": [[2]], "skillId": [6160, 61660], "ishideUI": 1}, "10050": {"id": 10050, "name": "�ɽ�", "rarity": 2, "desc": "<outline color=black width=2>���콣�ķ����ٶ�<color=#FFDD42>+50%</color>�˺�<color=#FFDD42>+30%</color></outline>", "type": 3, "time": -1, "isOverlay": 2, "icon": "v1/images/equip/item_20143", "attr": [133, 100], "value": [[0.5], [0.3]], "skillId": [6180, 61800]}, "10051": {"id": 10051, "name": "�˺�����1", "rarity": 2, "desc": "<outline color=black width=2>���콣�˺�<color=#FFDD42>+50%</color></outline>", "type": 3, "time": -1, "isOverlay": 10, "icon": "v1/images/equip/item_20143", "attr": [100], "value": [[0.5]], "skillId": [6180, 61800], "ishideUI": 1}, "10052": {"id": 10052, "name": "�޴�", "rarity": 2, "desc": "<outline color=black width=2>���콣���<color=#FFDD42>+30%</color></outline>", "type": 3, "time": -1, "isOverlay": 2, "icon": "v1/images/equip/item_20143", "attr": [102], "value": [[0.3]], "skillId": [6180, 61800], "ishideUI": 1}, "10053": {"id": 10053, "name": "˫��", "rarity": 2, "desc": "<outline color=black width=2>���콣����<color=#FFDD42>+2</color></outline>", "type": 3, "time": -1, "isOverlay": 2, "icon": "v1/images/equip/item_20143", "attr": [101], "value": [[2]], "skillId": [6180], "ishideUI": 1}, "10054": {"id": 10054, "name": "�����", "rarity": 4, "desc": "<outline color=black width=2>����<color=#FFDD42>����</color>���콣</outline>", "type": 3, "time": -1, "icon": "v1/images/equip/item_20143", "attr": [101], "value": [[10]], "skillId": [6180], "ishideUI": 1}, "10055": {"id": 10055, "name": "����", "rarity": 3, "desc": "<outline color=black width=2>���콣������<color=#FFDD42>+15%</color></outline>", "type": 3, "time": -1, "isOverlay": 2, "icon": "v1/images/equip/item_20143", "attr": [148], "value": [[0.15]], "skillId": [6180, 61800], "ishideUI": 1}, "10056": {"id": 10056, "name": "����", "rarity": 3, "desc": "<outline color=black width=2>���콣�����˺�<color=#FFDD42>+25%</color></outline>", "type": 3, "time": -1, "isOverlay": 4, "icon": "v1/images/equip/item_20143", "attr": [149], "value": [[0.25]], "skillId": [6180, 61800], "ishideUI": 1}, "10057": {"id": 10057, "name": "�˺�����2", "rarity": 3, "desc": "<outline color=black width=2>���콣�˺�<color=#FFDD42>+200%</color></outline>", "type": 3, "time": -1, "isOverlay": 3, "icon": "v1/images/equip/item_20143", "attr": [100], "value": [[2]], "skillId": [6180, 61800], "ishideUI": 1}, "10058": {"id": 10058, "name": "���콣��", "rarity": 4, "desc": "<outline color=black width=2>�ٻ�<color=#FFDD42>���콣��</color></outline>", "type": 3, "time": -1, "icon": "v1/images/equip/item_20143", "attr": [143], "value": [[61800]], "skillId": [6180], "ishideUI": 1}, "10060": {"id": 10060, "name": "�����ӿ�", "rarity": 3, "desc": "<outline color=black width=2>��˫�ؽ��ӿ�<color=#FFDD42>3��</color></outline>", "type": 3, "time": -1, "icon": "img/ModeBackpackHero/bufficon/wsjrlj", "attr": [101], "value": [[2]], "skillId": [6200], "ishideUI": 1}, "10061": {"id": 10061, "name": "�ٶ�����", "rarity": 2, "desc": "<outline color=black width=2>�캮�ؽ��˺�<color=#FFDD42>+50%</color></outline>", "type": 3, "time": -1, "isOverlay": 6, "icon": "v1/images/equip/item_20065", "attr": [100], "value": [[0.5]], "skillId": [6200], "ishideUI": 1}, "10062": {"id": 10062, "name": "�캮����", "rarity": 4, "desc": "<outline color=black width=2>�캮�ؽ���������<color=#FFDD42>+4</color></outline>", "type": 3, "time": -1, "icon": "v1/images/equip/item_20065", "attr": [101], "value": [[4]], "skillId": [62000], "ishideUI": 1}, "10063": {"id": 10063, "name": "�ٶ�����", "rarity": 2, "desc": "<outline color=black width=2>�캮�ؽ��ٶ�<color=#FFDD42>+25%</color></outline>", "type": 3, "time": -1, "isOverlay": 2, "icon": "v1/images/equip/item_20065", "attr": [103], "value": [[-0.25]], "skillId": [6200], "ishideUI": 1}, "10064": {"id": 10064, "name": "�޽�֮��", "rarity": 3, "desc": "<outline color=black width=2>�캮�ؽ����佣�����<color=#FFDD42>+25%</color></outline>", "type": 3, "time": -1, "isOverlay": 2, "icon": "v1/images/equip/item_20065", "attr": [102], "value": [[0.25]], "skillId": [6200, 62000], "ishideUI": 1}, "10065": {"id": 10065, "name": "��������", "rarity": 4, "desc": "<outline color=black width=2>�캮�ؽ����б�Ϊ��ɫ,���е�λ�ض�<color=#FFDD42>����</color></outline>", "type": 3, "time": -1, "icon": "v1/images/equip/item_20065", "attr": [148, 135], "value": [[1], [1061]], "skillId": [62000], "ishideUI": 1}, "10066": {"id": 10066, "name": "�����˺�", "rarity": 2, "desc": "<outline color=black width=2>�캮�ؽ������˺�<color=#FFDD42>+50%</color></outline>", "type": 3, "time": -1, "isOverlay": 5, "icon": "v1/images/equip/item_20065", "attr": [100], "value": [[0.5]], "skillId": [62000], "ishideUI": 1}, "10067": {"id": 10067, "name": "һ����", "rarity": 4, "desc": "<outline color=black width=2>�캮�ؽ��ͽ����˺�<color=#FFDD42>+500%</color>�����˺�<color=#FFDD42>+100%</color>���ٶ�<color=#FFDD42>-50%</color></outline>", "type": 3, "time": -1, "icon": "v1/images/equip/item_20065", "attr": [100, 149, 103], "value": [[5], [1], [0.5]], "skillId": [6200, 62000], "ishideUI": 1}, "10068": {"id": 10068, "name": "������", "rarity": 3, "desc": "<outline color=black width=2>�캮�ؽ��ͽ����˺�<color=#FFDD42>+60%</color></outline>", "type": 3, "time": -1, "isOverlay": 4, "icon": "v1/images/equip/item_20065", "attr": [100], "value": [[0.6]], "skillId": [6200, 62000], "ishideUI": 1}, "10069": {"id": 10069, "name": "��������", "rarity": 3, "desc": "<outline color=black width=2>�캮�ؽ���������<color=#FFDD42>+1</color></outline>", "type": 3, "time": -1, "isOverlay": 4, "icon": "v1/images/equip/item_20065", "attr": [101], "value": [[1]], "skillId": [62000], "ishideUI": 1}, "10070": {"id": 10070, "name": "�˺�����1", "rarity": 2, "desc": "<outline color=black width=2>����ӡ�˺�<color=#FFDD42>+50%</color></outline>", "type": 3, "time": -1, "isOverlay": 10, "icon": "v1/images/equip/item_20164", "attr": [100], "value": [[0.5]], "skillId": [6220, 62200], "ishideUI": 1}, "10071": {"id": 10071, "name": "�ٶ�����", "rarity": 2, "desc": "<outline color=black width=2>����ӡ�ٶ�<color=#FFDD42>+25%</color></outline>", "type": 3, "time": -1, "isOverlay": 2, "icon": "v1/images/equip/item_20164", "attr": [103], "value": [[-0.25]], "skillId": [6220, 62200], "ishideUI": 1}, "10072": {"id": 10072, "name": "����ӡ", "rarity": 4, "desc": "<outline color=black width=2>����ӡ������ͷ�<color=#FFDD42>����ӡ</color>�˺�<color=#FFDD42>+100%</color></outline>", "type": 3, "time": -1, "icon": "v1/images/equip/item_20164", "attr": [143], "value": [[62200]], "skillId": [6220], "ishideUI": 1}, "10073": {"id": 10073, "name": "ӡ����", "rarity": 2, "desc": "<outline color=black width=2>����ӡ���<color=#FFDD42>+15%</color></outline>", "type": 3, "time": -1, "isOverlay": 3, "icon": "v1/images/equip/item_20164", "attr": [102], "value": [[0.15]], "skillId": [6220, 62200], "ishideUI": 1}, "10074": {"id": 10074, "name": "��֮��", "rarity": 3, "desc": "<outline color=black width=2>����ӡ�����ͷ�<color=#FFDD42>1��</color></outline>", "type": 3, "time": -1, "isOverlay": 1, "icon": "v1/images/equip/item_20164", "attr": [108], "value": [[1]], "skillId": [6220, 62200], "ishideUI": 1}, "10075": {"id": 10075, "name": "����", "rarity": 3, "desc": "<outline color=black width=2>����ӡ������<color=#FFDD42>+25%</color></outline>", "type": 3, "time": -1, "isOverlay": 2, "icon": "v1/images/equip/item_20164", "attr": [148], "value": [[0.25]], "skillId": [6220, 62200], "ishideUI": 1}, "10076": {"id": 10076, "name": "�����˺�", "rarity": 3, "desc": "<outline color=black width=2>����ӡ�����˺�<color=#FFDD42>+100%</color></outline>", "type": 3, "time": -1, "icon": "v1/images/equip/item_20164", "attr": [149], "value": [[1]], "skillId": [6220, 62200], "ishideUI": 1}, "10077": {"id": 10077, "name": "�˺�����2", "rarity": 3, "desc": "<outline color=black width=2>����ӡ�˺�<color=#FFDD42>+200%</color></outline>", "type": 3, "time": -1, "isOverlay": 3, "icon": "v1/images/equip/item_20164", "attr": [100], "value": [[2]], "skillId": [6220, 62200], "ishideUI": 1}, "10078": {"id": 10078, "name": "���츴��", "rarity": 4, "desc": "<outline color=black width=2>����ӡÿ���ͷ����е�λ�˺��������<color=#FFDD42>20%</color>���10��</outline>", "type": 3, "time": -1, "isOverlay": 10, "icon": "v1/images/equip/item_20164", "attr": [100], "value": [[0.2]], "skillId": [6220, 62200], "ishideUI": 1, "className": "<PERSON><PERSON>_<PERSON><PERSON><PERSON><PERSON><PERSON>", "otherValue": [1.3], "isSelect": 1}, "10080": {"id": 10080, "name": "�˺�����1", "rarity": 2, "desc": "<outline color=black width=2>�Ͻ��«�˺�<color=#FFDD42>+50%</color></outline>", "type": 3, "time": -1, "isOverlay": 10, "icon": "v1/images/equip/item_20023", "attr": [100], "value": [[0.5]], "skillId": [6240, 62420], "ishideUI": 1}, "10081": {"id": 10081, "name": "�ٶ�����", "rarity": 2, "desc": "<outline color=black width=2>�Ͻ��«�ٶ�<color=#FFDD42>+25%</color></outline>", "type": 3, "time": -1, "isOverlay": 2, "icon": "v1/images/equip/item_20023", "attr": [103], "value": [[-0.25]], "skillId": [6240, 62420], "ishideUI": 1}, "10082": {"id": 10082, "name": "�����ڶ�", "rarity": 4, "desc": "<outline color=black width=2>�ڶ�����׵�ڶ����˺�<color=#FFDD42>+200%</color>ͬʱ�������ܴ�������</outline>", "type": 3, "time": -1, "icon": "v1/images/equip/item_20023", "attr": [128, 104, 102, 100, 135, 122], "value": [[2], [2], [0.1], [2], [1351], [62420, 2, 3, 0.2]], "skillId": [6240], "ishideUI": 1}, "10083": {"id": 10083, "name": "�ڶ�����", "rarity": 2, "desc": "<outline color=black width=2>�ڶ����<color=#FFDD42>+25%</color></outline>", "type": 3, "time": -1, "isOverlay": 2, "icon": "v1/images/equip/item_20023", "attr": [102], "value": [[0.25]], "skillId": [6240, 62420], "ishideUI": 1}, "10084": {"id": 10084, "name": "��������", "rarity": 3, "desc": "<outline color=black width=2>�ڶ����к����<color=#FFDD42>4</color>��С�ڶ�</outline>", "type": 3, "time": -1, "icon": "v1/images/equip/item_20023", "attr": [122], "value": [[62400, 5, 3, 0]], "skillId": [6240, 62420], "ishideUI": 1}, "10085": {"id": 10085, "name": "˺��", "rarity": 3, "desc": "<outline color=black width=2>�Ͻ��«�ڶ��˺����<color=#FFDD42>-25%</color></outline>", "type": 3, "time": -1, "isOverlay": 2, "icon": "v1/images/equip/item_20023", "attr": [105], "value": [[-0.25]], "skillId": [6240, 62420], "ishideUI": 1}, "10086": {"id": 10086, "name": "�˺�����2", "rarity": 3, "desc": "<outline color=black width=2>�Ͻ��«�˺�<color=#FFDD42>+200%</color></outline>", "type": 3, "time": -1, "isOverlay": 3, "icon": "v1/images/equip/item_20023", "attr": [100], "value": [[2]], "skillId": [6240, 62420], "ishideUI": 1}, "10087": {"id": 10087, "name": "����", "rarity": 3, "desc": "<outline color=black width=2>�Ͻ��«������<color=#FFDD42>+25%</color></outline>", "type": 3, "time": -1, "isOverlay": 3, "icon": "v1/images/equip/item_20023", "attr": [148], "value": [[0.25]], "skillId": [6240, 62420], "ishideUI": 1}, "10088": {"id": 10088, "name": "����", "rarity": 3, "desc": "<outline color=black width=2>�Ͻ��«�����˺�<color=#FFDD42>+50%</color></outline>", "type": 3, "time": -1, "isOverlay": 4, "icon": "v1/images/equip/item_20023", "attr": [149], "value": [[1]], "skillId": [6240, 62420], "ishideUI": 1}, "10089": {"id": 10089, "name": "�������", "rarity": 3, "desc": "<outline color=black width=2>�Ͻ��«�ڶ����<color=#FFDD42>+80%</color>�˺�<color=#FFDD42>+50%</color></outline>", "type": 3, "time": -1, "isOverlay": 2, "icon": "v1/images/equip/item_20023", "attr": [102, 100], "value": [[0.8], [0.5]], "skillId": [6240, 62420], "ishideUI": 1}, "10090": {"id": 10090, "name": "��������", "rarity": 2, "desc": "<outline color=black width=2>�𹿰�����ʱ��<color=#FFDD42>+2s</color></outline>", "type": 3, "time": -1, "isOverlay": 4, "icon": "img/ModeBackpackHero/bufficon/cxhw", "attr": [104, 128], "value": [[2], [2]], "skillId": [6260]}, "10091": {"id": 10091, "name": "�ջ��", "rarity": 3, "desc": "<outline color=black width=2>�𹿰���������Ч�����<color=#FFDD42>+10%</color>,���е�λ������</outline>", "type": 3, "time": -1, "icon": "img/ModeBackpackHero/bufficon/jgshg", "attr": [135, 102, 109], "value": [[1851], [0.1], [100910, 5, 3, 0]], "skillId": [6260, 62660]}, "10092": {"id": 10092, "name": "�˺�����1", "rarity": 2, "desc": "<outline color=black width=2>���������˺�<color=#FFDD42>+50%</color></outline>", "type": 3, "time": -1, "isOverlay": 10, "icon": "v1/images/equip/item_20173", "attr": [100], "value": [[0.5]], "skillId": [6260, 146000, 146020, 146040], "ishideUI": 1}, "10093": {"id": 10093, "name": "����", "rarity": 3, "desc": "<outline color=black width=2>����������ת��������ͷ�1�δ�Χ�������<color=#FFDD42>150%</color>�˺�</outline>", "type": 3, "time": -1, "icon": "v1/images/equip/item_20173", "attr": [122], "value": [[62620, 4, 8, 0]], "skillId": [6260], "ishideUI": 1}, "10094": {"id": 10094, "name": "�ٶ�����", "rarity": 2, "desc": "<outline color=black width=2>���������ٶ�<color=#FFDD42>+25%</color></outline>", "type": 3, "time": -1, "isOverlay": 2, "icon": "v1/images/equip/item_20173", "attr": [103], "value": [[-0.25]], "skillId": [6260], "ishideUI": 1}, "10095": {"id": 10095, "name": "��������", "rarity": 4, "desc": "<outline color=black width=2>������������ת�ڼ���ͷ�<color=#FFDD42>����</color>���ض�<color=#FFDD42>����</color></outline>", "type": 3, "time": -1, "icon": "v1/images/equip/item_20173", "attr": [143], "value": [[62600]], "skillId": [6260], "ishideUI": 1, "listenObject": 1}, "10096": {"id": 10096, "name": "���", "rarity": 2, "desc": "<outline color=black width=2>�����������<color=#FFDD42>+25%</color></outline>", "type": 3, "time": -1, "isOverlay": 2, "icon": "v1/images/equip/item_20173", "attr": [102], "value": [[0.25]], "skillId": [6260], "ishideUI": 1}, "10097": {"id": 10097, "name": "����", "rarity": 3, "desc": "<outline color=black width=2>���������ͷ�ʱ�����<color=#FFDD42>����</color></outline>", "type": 3, "time": -1, "icon": "v1/images/equip/item_20173", "attr": [122], "value": [[62640, 2, 1, 0]], "skillId": [6260, 62660], "ishideUI": 1}, "10098": {"id": 10098, "name": "��ʥ", "rarity": 4, "desc": "<outline color=black width=2>���������˺�<color=#FFDD42>+300%</color>������<color=#FFDD42>+25%</color>�����˺�<color=#FFDD42>+100%</color></outline>", "type": 3, "time": -1, "icon": "v1/images/equip/item_20173", "attr": [100, 148, 149], "value": [[3], [0.25], [1]], "skillId": [6260, 62660], "ishideUI": 1}, "10099": {"id": 10099, "name": "�˺�����2", "rarity": 3, "desc": "<outline color=black width=2>���������˺�<color=#FFDD42>+200%</color></outline>", "type": 3, "time": -1, "isOverlay": 3, "icon": "v1/images/equip/item_20173", "attr": [100], "value": [[2]], "skillId": [6260, 146000, 146020, 146040, 62660], "ishideUI": 1}, "10100": {"id": 10100, "name": "�˺�����1", "rarity": 2, "desc": "<outline color=black width=2>�����˺�<color=#FFDD42>+50%</color></outline>", "type": 3, "time": -1, "isOverlay": 10, "icon": "v1/images/equip/item_20045", "attr": [100], "value": [[0.5]], "skillId": [6280], "ishideUI": 1}, "10101": {"id": 10101, "name": "��", "rarity": 3, "desc": "<outline color=black width=2>���޵����Ե���<color=#FFDD42>����</color></outline>", "type": 3, "time": -1, "icon": "v1/images/equip/item_20045", "attr": [500, 104], "value": [[1], [6]], "skillId": [6280], "ishideUI": 1}, "10102": {"id": 10102, "name": "�ٶ�����", "rarity": 2, "desc": "<outline color=black width=2>�����ٶ�<color=#FFDD42>+25%</color></outline>", "type": 3, "time": -1, "isOverlay": 3, "icon": "v1/images/equip/item_20045", "attr": [103], "value": [[-0.25]], "skillId": [6280], "ishideUI": 1}, "10103": {"id": 10103, "name": "����֮��", "rarity": 4, "desc": "<outline color=black width=2>���޷���ʱ�����<color=#FFDD42>Խ��Խ��</color>,�˺�Ҳ����<color=#FFDD42>����</color></outline>", "type": 3, "time": -1, "icon": "v1/images/equip/item_20045", "attr": [109, 109], "value": [[101030, 15, 20, 0], [101031, 15, 20, 0]], "skillId": [6280], "ishideUI": 1}, "10104": {"id": 10104, "name": "���ޱ�Ϯ1", "rarity": 2, "desc": "<outline color=black width=2>��������<color=#FFDD42>+1</color></outline>", "type": 3, "time": -1, "isOverlay": 3, "icon": "v1/images/equip/item_20045", "attr": [101], "value": [[1]], "skillId": [6280], "ishideUI": 1}, "10105": {"id": 10105, "name": "����", "rarity": 3, "desc": "<outline color=black width=2>���ޱ�����<color=#FFDD42>+25%</color></outline>", "type": 3, "time": -1, "isOverlay": 3, "icon": "v1/images/equip/item_20045", "attr": [148], "value": [[0.25]], "skillId": [6280], "ishideUI": 1}, "10106": {"id": 10106, "name": "����", "rarity": 3, "desc": "<outline color=black width=2>���ޱ����˺�<color=#FFDD42>+100%</color></outline>", "type": 3, "time": -1, "isOverlay": 2, "icon": "v1/images/equip/item_20045", "attr": [149], "value": [[1]], "skillId": [6280], "ishideUI": 1}, "10107": {"id": 10107, "name": "�˺�����2", "rarity": 3, "desc": "<outline color=black width=2>�����˺�<color=#FFDD42>+100%</color></outline>", "type": 3, "time": -1, "isOverlay": 3, "icon": "v1/images/equip/item_20045", "attr": [100], "value": [[1]], "skillId": [6280], "ishideUI": 1}, "10108": {"id": 10108, "name": "���ޱ�Ϯ2", "rarity": 3, "desc": "<outline color=black width=2>��������<color=#FFDD42>+3</color></outline>", "type": 3, "time": -1, "isOverlay": 2, "icon": "v1/images/equip/item_20045", "attr": [101], "value": [[3]], "skillId": [6280], "ishideUI": 1}, "10110": {"id": 10110, "name": "�˺�����1", "rarity": 2, "desc": "<outline color=black width=2>������˺�<color=#FFDD42>+50%</color></outline>", "type": 3, "time": -1, "isOverlay": 10, "icon": "v1/images/equip/item_20025", "attr": [100], "value": [[0.5]], "skillId": [6300, 63000, 63020], "ishideUI": 1}, "10111": {"id": 10111, "name": "�ٶ�����", "rarity": 2, "desc": "<outline color=black width=2>������ٶ�<color=#FFDD42>+25%</color></outline>", "type": 3, "time": -1, "isOverlay": 3, "icon": "v1/images/equip/item_20025", "attr": [103], "value": [[-0.25]], "skillId": [6300], "ishideUI": 1}, "10112": {"id": 10112, "name": "����", "rarity": 4, "desc": "<outline color=black width=2>������ͷŹ��̻��������<color=#FFDD42>����</color></outline>", "type": 3, "time": -1, "icon": "v1/images/equip/item_20025", "attr": [122], "value": [[63000, 2, 2, 0]], "skillId": [6300, 63020, 7300], "ishideUI": 1}, "10113": {"id": 10113, "name": "����", "rarity": 2, "desc": "<outline color=black width=2>���������<color=#FFDD42>+2</color></outline>", "type": 3, "time": -1, "isOverlay": 3, "icon": "v1/images/equip/item_20025", "attr": [101], "value": [[2]], "skillId": [6300], "ishideUI": 1}, "10114": {"id": 10114, "name": "����", "rarity": 3, "desc": "<outline color=black width=2>������ͷź�����<color=#FFDD42>���</color></outline>", "type": 3, "time": -1, "icon": "v1/images/equip/item_20025", "attr": [109], "value": [[602115, 15, 20, 0]], "skillId": [6300, 63020], "ishideUI": 1}, "10115": {"id": 10115, "name": "����", "rarity": 3, "desc": "<outline color=black width=2>����ֱ�����<color=#FFDD42>+25%</color></outline>", "type": 3, "time": -1, "isOverlay": 2, "icon": "v1/images/equip/item_20025", "attr": [148], "value": [[0.25]], "skillId": [6300, 63000, 63020], "ishideUI": 1}, "10116": {"id": 10116, "name": "����", "rarity": 3, "desc": "<outline color=black width=2>����ֱ����˺�<color=#FFDD42>+100%</color></outline>", "type": 3, "time": -1, "icon": "v1/images/equip/item_20025", "attr": [149], "value": [[1]], "skillId": [6300, 63000, 63020], "ishideUI": 1}, "10117": {"id": 10117, "name": "����֮��", "rarity": 4, "desc": "<outline color=black width=2>�����ͣ��������<color=#FFDD42>����</color></outline>", "type": 3, "time": -1, "icon": "v1/images/equip/item_20025", "attr": [122], "value": [[63020, 2, 2, 3]], "skillId": [6300], "ishideUI": 1}, "10118": {"id": 10118, "name": "�˺�����2", "rarity": 3, "desc": "<outline color=black width=2>������˺�<color=#FFDD42>+150%</color></outline>", "type": 3, "time": -1, "isOverlay": 3, "icon": "v1/images/equip/item_20025", "attr": [100], "value": [[1.5]], "skillId": [6300, 63000, 63020], "ishideUI": 1}, "10119": {"id": 10119, "name": "����", "rarity": 3, "desc": "<outline color=black width=2>��������к����ʹ��λ����<color=#FFDD42>20%</color>����2s</outline>", "type": 3, "time": -1, "icon": "v1/images/equip/item_20025", "attr": [109], "value": [[101190, 5, 3, 0]], "skillId": [6300], "ishideUI": 1}, "10120": {"id": 10120, "name": "����ŷ�", "rarity": 3, "desc": "<outline color=black width=2>�ű����׵�������<color=#FFDD42>+2</color></outline>", "type": 3, "time": -1, "isOverlay": 2, "icon": "img/ModeBackpackHero/bufficon/dltt", "attr": [101], "value": [[2]], "skillId": [6320]}, "10121": {"id": 10121, "name": "��ѹ��", "rarity": 3, "desc": "<outline color=black width=2>�ű����׵����˺�<color=#FFDD42>+100%</color></outline>", "type": 3, "time": -1, "isOverlay": 4, "icon": "img/ModeBackpackHero/bufficon/dlgyd", "attr": [100], "value": [[1]], "skillId": [6320]}, "10122": {"id": 10122, "name": "�˺�����1", "rarity": 2, "desc": "<outline color=black width=2>�𱬷��˺�<color=#FFDD42>+50%</color></outline>", "type": 3, "time": -1, "isOverlay": 10, "icon": "v1/images/equip/item_20016", "attr": [100], "value": [[0.5]], "skillId": [6320, 63220], "ishideUI": 1}, "10123": {"id": 10123, "name": "����", "rarity": 3, "desc": "<outline color=black width=2>�𱬷�����ɵ������<color=#FFDD42>+1</color></outline>", "type": 3, "time": -1, "isOverlay": 2, "icon": "v1/images/equip/item_20016", "attr": [500], "value": [[1]], "skillId": [63220], "ishideUI": 1}, "10124": {"id": 10124, "name": "�ȶ�", "rarity": 2, "desc": "<outline color=black width=2>�𱬷�����ʱ��<color=#FFDD42>+4</color>��</outline>", "type": 3, "time": -1, "isOverlay": 2, "icon": "v1/images/equip/item_20016", "attr": [104], "value": [[4]], "skillId": [6320], "ishideUI": 1}, "10125": {"id": 10125, "name": "���浯", "rarity": 4, "desc": "<outline color=black width=2>�𱬷���ը��ᷢ��<color=#FFDD42>�����Ļ���</color></outline>", "type": 3, "time": -1, "icon": "v1/images/equip/item_20016", "attr": [101], "value": [[20]], "skillId": [63220], "ishideUI": 1}, "10126": {"id": 10126, "name": "�����𱬷�", "rarity": 2, "desc": "<outline color=black width=2>�𱬷�����<color=#FFDD42>+1</color></outline>", "type": 3, "time": -1, "icon": "v1/images/equip/item_20016", "attr": [101], "value": [[1]], "skillId": [6320], "ishideUI": 1}, "10127": {"id": 10127, "name": "��ը", "rarity": 3, "desc": "<outline color=black width=2>�𱬷���ը������4��<color=#FFDD42>��ը</color></outline>", "type": 3, "time": -1, "icon": "v1/images/equip/item_20016", "attr": [122], "value": [[63240, 3, 2, 0]], "skillId": [6320, 63200], "ishideUI": 1}, "10128": {"id": 10128, "name": "�޴�", "rarity": 3, "desc": "<outline color=black width=2>�𱬷����<color=#FFDD42>+50%</color></outline>", "type": 3, "time": -1, "isOverlay": 2, "icon": "v1/images/equip/item_20016", "attr": [102], "value": [[0.5]], "skillId": [6320, 63200], "ishideUI": 1}, "10129": {"id": 10129, "name": "�˺�����2", "rarity": 3, "desc": "<outline color=black width=2>�𱬷��˺�<color=#FFDD42>+200%</color></outline>", "type": 3, "time": -1, "isOverlay": 3, "icon": "v1/images/equip/item_20016", "attr": [100], "value": [[2]], "skillId": [6320, 63200], "ishideUI": 1}, "10130": {"id": 10130, "name": "�˺�����1", "rarity": 2, "desc": "<outline color=black width=2>�������˺�<color=#FFDD42>+50%</color></outline>", "type": 3, "time": -1, "isOverlay": 10, "icon": "v1/images/equip/item_20103", "attr": [100], "value": [[0.5]], "skillId": [6340, 63400, 63420], "ishideUI": 1}, "10131": {"id": 10131, "name": "���ٲ���", "rarity": 2, "desc": "<outline color=black width=2>�������ٶ�<color=#FFDD42>+25%</color></outline>", "type": 3, "time": -1, "isOverlay": 2, "icon": "v1/images/equip/item_20103", "attr": [103], "value": [[-0.25]], "skillId": [6340], "ishideUI": 1}, "10132": {"id": 10132, "name": "���⾵��", "rarity": 2, "desc": "<outline color=black width=2>����������<color=#FFDD42>+1</color></outline>", "type": 3, "time": -1, "icon": "v1/images/equip/item_20103", "attr": [101], "value": [[1]], "skillId": [6340], "ishideUI": 1}, "10133": {"id": 10133, "name": "��ɫ����", "rarity": 3, "desc": "<outline color=black width=2>�����������ɺ�ɫ��ÿ���˺�����<color=#FFDD42>5%</color>����100%</outline>", "type": 3, "time": -1, "isOverlay": 20, "icon": "v1/images/equip/item_20103", "attr": [135, 100], "value": [[1262], [0.05]], "skillId": [63400], "ishideUI": 1, "className": "Buff_OnSpawnHurt", "isSelect": 1}, "10134": {"id": 10134, "name": "����", "rarity": 3, "desc": "<outline color=black width=2>����������ʱ��<color=#FFDD42>+3s</color></outline>", "type": 3, "time": -1, "icon": "v1/images/equip/item_20103", "attr": [128], "value": [[3]], "skillId": [6340]}, "10135": {"id": 10135, "name": "��Ƶ����", "rarity": 3, "desc": "<outline color=black width=2>�������ļ��������˺��������<color=#FFDD42>25%</color></outline>", "type": 3, "time": -1, "isOverlay": 2, "icon": "v1/images/equip/item_20103", "attr": [105], "value": [[-0.25]], "skillId": [63400]}, "10136": {"id": 10136, "name": "�־�����", "rarity": 4, "desc": "<outline color=black width=2>�������������<color=#FFDD42>����ʱ��</color></outline>", "type": 3, "time": -1, "icon": "v1/images/equip/item_20103", "attr": [128], "value": [[30]], "skillId": [6340, 63400], "ishideUI": 1}, "10137": {"id": 10137, "name": "�˺�����2", "rarity": 3, "desc": "<outline color=black width=2>�������˺�<color=#FFDD42>+200%</color></outline>", "type": 3, "time": -1, "isOverlay": 3, "icon": "v1/images/equip/item_20103", "attr": [100], "value": [[2]], "skillId": [6340, 63400, 63420], "ishideUI": 1}, "10140": {"id": 10140, "name": "��������", "rarity": 2, "desc": "<outline color=black width=2>ȼ��ƿ�˺�<color=#FFDD42>+100%</color></outline>", "type": 3, "time": -1, "icon": "img/ModeBackpackHero/bufficon/rspgwzs", "attr": [100], "value": [[1]], "skillId": [6360]}, "10141": {"id": 10141, "name": "��������", "rarity": 2, "desc": "<outline color=black width=2>ȼ��ƿ�ٶ�<color=#FFDD42>+30%</color></outline>", "type": 3, "time": -1, "isOverlay": 2, "icon": "img/ModeBackpackHero/bufficon/rspjszz", "attr": [103], "value": [[-0.3]], "skillId": [6360]}, "10142": {"id": 10142, "name": "��������", "rarity": 3, "desc": "<outline color=black width=2>ȼ��ƿ���к����Χ���<color=#FFDD42>��Χ�˺�</color></outline>", "type": 3, "time": -1, "icon": "img/ModeBackpackHero/bufficon/rspfw", "attr": [122], "value": [[73600, 5, 3, 0]], "skillId": [6360]}, "10143": {"id": 10143, "name": "Խ��Խ��", "rarity": 3, "desc": "<outline color=black width=2>ȼ��ƿ���ճ���ʱ��<color=#FFDD42>+3s</color></outline>", "type": 3, "time": -1, "icon": "img/ModeBackpackHero/bufficon/rspdur", "attr": [104], "value": [[3]], "skillId": [6360]}, "10150": {"id": 10150, "name": "���طɸ�", "rarity": 2, "desc": "<outline color=black width=2>�ɸ�ÿ�ι�����,�´�Ͷ���ķɸ�����<color=#FFDD42>+1</color></outline>", "type": 3, "time": -1, "icon": "img/ModeBackpackHero/bufficon/fwdc", "attr": [108], "value": [[1]], "skillId": [6380], "className": "B<PERSON>_NextSkillAdd", "otherValue": [1]}, "10151": {"id": 10151, "name": "�����ɸ�", "rarity": 2, "desc": "<outline color=black width=2>�ɸ�ÿ�ι�����,�´��˺�<color=#FFDD42>+50%</color></outline>", "type": 3, "time": -1, "icon": "img/ModeBackpackHero/bufficon/fwfl", "attr": [100], "value": [[1.5]], "skillId": [6380], "className": "B<PERSON>_NextSkillAdd", "otherValue": [1]}, "10152": {"id": 10152, "name": "���ٷɸ�", "rarity": 2, "desc": "<outline color=black width=2>�ɸ�ÿ�ι�����,�´��ٶ�<color=#FFDD42>+20%</color></outline>", "type": 3, "time": -1, "icon": "img/ModeBackpackHero/bufficon/fwgs", "attr": [103], "value": [[-0.2]], "skillId": [6380], "className": "B<PERSON>_NextSkillAdd", "otherValue": [1]}, "10153": {"id": 10153, "name": "�����ɸ�", "rarity": 3, "desc": "<outline color=black width=2>�ɸ�ÿ�ι�����,�´α�����<color=#FFDD42>+50%</color></outline>", "type": 3, "time": -1, "icon": "img/ModeBackpackHero/bufficon/fwzm", "attr": [5], "value": [[0.5]], "skillId": [6380], "className": "B<PERSON>_NextSkillAdd", "otherValue": [1]}, "10160": {"id": 10160, "name": "����", "rarity": 2, "desc": "<outline color=black width=2>ÿ�ι��������ڶ���<color=#FFDD42>����</color></outline>", "type": 3, "time": -1, "icon": "img/ModeBackpackHero/bufficon/hxbhx", "attr": [-1], "value": [[0]], "skillId": [6400]}, "10161": {"id": 10161, "name": "��������", "rarity": 2, "desc": "<outline color=black width=2>�������˺�<color=#FFDD42>+100%</color></outline>", "type": 3, "time": -1, "icon": "img/ModeBackpackHero/bufficon/hxbsh", "attr": [100], "value": [[1]], "skillId": [6400]}, "10162": {"id": 10162, "name": "���ͻ���", "rarity": 2, "desc": "<outline color=black width=2>���������<color=#FFDD42>+100%</color></outline>", "type": 3, "time": -1, "icon": "img/ModeBackpackHero/bufficon/hxbjx", "attr": [102], "value": [[1]], "skillId": [6400]}, "10170": {"id": 10170, "name": "�ѻ�����", "rarity": 3, "desc": "<outline color=black width=2>�ѻ�ǹ������<color=#FFDD42>+25%</color></outline>", "type": 1, "time": -1, "icon": "img/buff/icon/bw_jjbjl", "attr": [5], "value": [[0.25]], "skillId": [6420]}, "10171": {"id": 10171, "name": "�����˺�", "rarity": 3, "desc": "<outline color=black width=2>�ѻ�ǹ�����˺�<color=#FFDD42>+50%</color></outline>", "type": 1, "time": -1, "icon": "img/buff/icon/bw_jjbj2", "attr": [4], "value": [[1]], "skillId": [6420]}, "10172": {"id": 10172, "name": "�ѻ��˺�", "rarity": 2, "desc": "<outline color=black width=2>�ѻ�ǹ�˺�<color=#FFDD42>+50%</color></outline>", "type": 3, "time": -1, "icon": "img/buff/icon/bw_jjsh2", "attr": [100], "value": [[1.7]], "skillId": [6420]}, "10173": {"id": 10173, "name": "�ѻ�����", "rarity": 3, "desc": "<outline color=black width=2>�ѻ�ǹ�ӵ�����<color=#FFDD42>+1</color></outline>", "type": 3, "time": -1, "icon": "img/buff/icon/bw_jjzdft", "attr": [500], "value": [[1]], "skillId": [6420]}, "10180": {"id": 10180, "name": "ըҩר��", "rarity": 3, "desc": "<outline color=black width=2>ըҩ������<color=#FFDD42>+25%</color></outline>", "type": 1, "time": -1, "icon": "img/ModeBackpackHero/bufficon/zyzyzj", "attr": [5], "value": [[0.5]], "skillId": [6440]}, "10181": {"id": 10181, "name": "�����ը", "rarity": 3, "desc": "<outline color=black width=2>ըҩ����<color=#FFDD42>+2</color></outline>", "type": 3, "time": -1, "icon": "img/ModeBackpackHero/bufficon/zykhlz", "attr": [101], "value": [[2]], "skillId": [6440]}, "10182": {"id": 10182, "name": "��ը�˺�", "rarity": 2, "desc": "<outline color=black width=2>ըҩ��ը�˺�<color=#FFDD42>+50%</color></outline>", "type": 3, "time": -1, "icon": "img/ModeBackpackHero/bufficon/zybzsh", "attr": [100], "value": [[0.3]], "skillId": [6440]}, "10190": {"id": 10190, "name": "����", "rarity": 2, "desc": "<outline color=black width=2>���ױ�ը�����˵�λ��ʹ���ܵ����˺�<color=#FFDD42>+15%</color>����5��</outline>", "type": 3, "time": -1, "icon": "img/ModeBackpackHero/bufficon/dlzs", "attr": [109], "value": [[1040, 5, 3, 0]], "skillId": [6460]}, "10191": {"id": 10191, "name": "��ʱ��", "rarity": 3, "desc": "<outline color=black width=2>���״���ʱ��<color=#FFDD42>+3s</color></outline>", "type": 3, "time": -1, "icon": "img/ModeBackpackHero/bufficon/dlcxsj", "attr": [128], "value": [[3]], "skillId": [6460]}, "10192": {"id": 10192, "name": "˫����", "rarity": 3, "desc": "<outline color=black width=2>��������<color=#FFDD42>+3</color></outline>", "type": 3, "time": -1, "icon": "img/ModeBackpackHero/bufficon/dlsxp", "attr": [101], "value": [[3]], "skillId": [6460]}, "10200": {"id": 10200, "name": "˫�г�ì", "rarity": 2, "desc": "<outline color=black width=2>��ì�˺�<color=#FFDD42>+100%</color>��װ���ٶ�<color=#FFDD42>-25%</color></outline>", "type": 3, "time": -1, "icon": "img/ModeBackpackHero/bufficon/srcm", "attr": [100, 103], "value": [[2.5], [0.25]], "skillId": [6480]}, "10201": {"id": 10201, "name": "����֮ì", "rarity": 3, "desc": "<outline color=black width=2>ÿ������Ŀ�������5%ì��װ���ٶȣ�����<color=#FFDD42>5s</color>���ɵ���<color=#FFDD42>6��</color></outline>", "type": 3, "time": 5, "isOverlay": 6, "icon": "img/ModeBackpackHero/bufficon/gszm", "attr": [103], "value": [[-0.05]], "skillId": [6480], "className": "<PERSON><PERSON>_<PERSON><PERSON><PERSON><PERSON><PERSON>", "isSelect": 1}, "10210": {"id": 10210, "name": "����", "rarity": 3, "desc": "<outline color=black width=2>����1�δ��<color=#FFDD42>3</color>��</outline>", "type": 3, "time": -1, "icon": "img/ModeBackpackHero/bufficon/gbbf", "attr": [101], "value": [[2]], "skillId": [6500]}, "10211": {"id": 10211, "name": "����", "rarity": 2, "desc": "<outline color=black width=2>��������<color=#FFDD42>+15%</color></outline>", "type": 3, "time": -1, "icon": "img/ModeBackpackHero/bufficon/gbys", "attr": [102], "value": [[0.15]], "skillId": [6500]}, "10212": {"id": 10212, "name": "��������", "rarity": 3, "desc": "<outline color=black width=2>����������<color=#FFDD42>+15%</color></outline>", "type": 1, "time": -1, "icon": "img/ModeBackpackHero/bufficon/gbbj", "attr": [5], "value": [[0.1]], "skillId": [6500]}, "10213": {"id": 10213, "name": "����", "rarity": 3, "desc": "<outline color=black width=2>�������к�����<color=#FFDD42>����</color></outline>", "type": 3, "time": -1, "icon": "img/ModeBackpackHero/bufficon/gbbq", "attr": [122], "value": [[75000, 5, 3, 0]], "skillId": [6500]}, "10220": {"id": 10220, "name": "������", "rarity": 3, "desc": "<outline color=black width=2>̫��1�λӳ�<color=#FFDD42>3��</color></outline>", "type": 3, "time": -1, "icon": "img/ModeBackpackHero/bufficon/tdsdl", "attr": [105, 135], "value": [[0.2], [132]], "skillId": [6520]}, "10221": {"id": 10221, "name": "�ε���", "rarity": 3, "desc": "<outline color=black width=2>ÿ�ι������ε�<color=#FFDD42>2��</color></outline>", "type": 3, "time": -1, "icon": "img/ModeBackpackHero/bufficon/tdbds", "attr": [108], "value": [[1]], "skillId": [6520]}, "10222": {"id": 10222, "name": "����", "rarity": 3, "desc": "<outline color=black width=2>̫�������˺�<color=#FFDD42>+100%</color></outline>", "type": 1, "time": -1, "icon": "img/ModeBackpackHero/bufficon/tdhx", "attr": [4], "value": [[1]], "skillId": [6520]}, "10230": {"id": 10230, "name": "3֧��", "rarity": 2, "desc": "<outline color=black width=2>���Ϲ����<color=#FFDD42>3</color>֧��ʸ</outline>", "type": 3, "time": -1, "isOverlay": 3, "icon": "img/ModeBackpackHero/bufficon/jnsls", "attr": [101], "value": [[2]], "skillId": [6540]}, "10231": {"id": 10231, "name": "����", "rarity": 2, "desc": "<outline color=black width=2>���Ϲ��ɴ�͸��<color=#FFDD42>1</color>����λ</outline>", "type": 3, "time": -1, "icon": "img/ModeBackpackHero/bufficon/jndc", "attr": [120], "value": [[2]], "skillId": [6540]}, "10232": {"id": 10232, "name": "����", "rarity": 2, "desc": "<outline color=black width=2>��������������<color=#FFDD42>+2</color></outline>", "type": 3, "time": -1, "isOverlay": 3, "icon": "img/ModeBackpackHero/bufficon/jnwls", "attr": [108], "value": [[1]], "skillId": [6540]}, "10233": {"id": 10233, "name": "���ټ���", "rarity": 3, "desc": "<outline color=black width=2>���Ϲ�����<color=#FFDD42>+30%</color></outline>", "type": 3, "time": -1, "isOverlay": 2, "icon": "img/ModeBackpackHero/bufficon/jnjssj", "attr": [103], "value": [[-0.3]], "skillId": [6540]}, "10240": {"id": 10240, "name": "�˺�����1", "rarity": 2, "desc": "<outline color=black width=2>��������ƿ�˺�<color=#FFDD42>+50%</color></outline>", "type": 3, "time": -1, "isOverlay": 10, "icon": "v1/images/equip/item_20084", "attr": [100], "value": [[0.5]], "skillId": [6620, 66200], "ishideUI": 1}, "10241": {"id": 10241, "name": "�ٶ�����", "rarity": 2, "desc": "<outline color=black width=2>��������ƿ�ٶ�<color=#FFDD42>+25%</color></outline>", "type": 3, "time": -1, "isOverlay": 2, "icon": "v1/images/equip/item_20084", "attr": [103], "value": [[-0.25]], "skillId": [6620, 66200], "ishideUI": 1}, "10242": {"id": 10242, "name": "��������", "rarity": 4, "desc": "<outline color=black width=2>����<color=#FFDD42>����Χ��</color>����������</outline>", "type": 3, "time": -1, "icon": "v1/images/equip/item_20084", "attr": [139], "value": [[66200]], "skillId": [6620], "ishideUI": 1}, "10243": {"id": 10243, "name": "��������", "rarity": 2, "desc": "<outline color=black width=2>��������ƿ������ʱ��<color=#FFDD42>+2s</color></outline>", "type": 3, "time": -1, "isOverlay": 2, "icon": "v1/images/equip/item_20084", "attr": [104, 128], "value": [[2], [2]], "skillId": [6620, 66200], "ishideUI": 1}, "10244": {"id": 10244, "name": "�������", "rarity": 3, "desc": "<outline color=black width=2>��������ƿ�˺��������<color=#FFDD42>25%</color></outline>", "type": 3, "time": -1, "isOverlay": 2, "icon": "v1/images/equip/item_20084", "attr": [105], "value": [[-0.25]], "skillId": [6620, 66200], "ishideUI": 1}, "10245": {"id": 10245, "name": "����", "rarity": 3, "desc": "<outline color=black width=2>��������ƿ������<color=#FFDD42>+25%</color></outline>", "type": 3, "time": -1, "isOverlay": 2, "icon": "v1/images/equip/item_20084", "attr": [148], "value": [[0.25]], "skillId": [6620, 66200], "ishideUI": 1}, "10246": {"id": 10246, "name": "����", "rarity": 3, "desc": "<outline color=black width=2>��������ƿ�����˺�<color=#FFDD42>+100%</color></outline>", "type": 3, "time": -1, "isOverlay": 2, "icon": "v1/images/equip/item_20084", "attr": [149], "value": [[1]], "skillId": [6620, 66200], "ishideUI": 1}, "10247": {"id": 10247, "name": "�˺�����2", "rarity": 3, "desc": "<outline color=black width=2>��������ƿ�˺�<color=#FFDD42>+200%</color></outline>", "type": 3, "time": -1, "isOverlay": 3, "icon": "v1/images/equip/item_20084", "attr": [100], "value": [[2]], "skillId": [6620, 66200], "ishideUI": 1}, "10248": {"id": 10248, "name": "���ع���", "rarity": 4, "desc": "<outline color=black width=2>��������ƿ����<color=#FFDD42>+2</color></outline>", "type": 3, "time": -1, "icon": "v1/images/equip/item_20084", "attr": [101], "value": [[2]], "skillId": [6620, 66200], "ishideUI": 1}, "10340": {"id": 10340, "name": "��Ծ1", "rarity": 2, "desc": "<outline color=black width=2>��������Ծ����+1</outline>", "type": 3, "time": -1, "isOverlay": 4, "icon": "img/ModeBackpackHero/bufficon/skill_sdl", "attr": [153], "value": [[1]], "skillId": [6840], "ishideUI": 1}, "10341": {"id": 10341, "name": "��Ծ2", "rarity": 3, "desc": "<outline color=black width=2>��������Ծ����+2</outline>", "type": 3, "time": -1, "isOverlay": 2, "icon": "img/ModeBackpackHero/bufficon/skill_sdl", "attr": [153], "value": [[2]], "skillId": [6840], "ishideUI": 1}, "10342": {"id": 10342, "name": "�˺�����1", "rarity": 2, "desc": "<outline color=black width=2>�������˺�<color=#FFDD42>+30%</color></outline>", "type": 3, "time": -1, "isOverlay": 20, "icon": "img/ModeBackpackHero/bufficon/skill_sdl", "attr": [100], "value": [[0.3]], "skillId": [6840], "ishideUI": 1}, "10343": {"id": 10343, "name": "�˺�����2", "rarity": 3, "desc": "<outline color=black width=2>�������˺�<color=#FFDD42>+60%</color></outline>", "type": 3, "time": -1, "isOverlay": 10, "icon": "img/ModeBackpackHero/bufficon/skill_sdl", "attr": [100], "value": [[0.6]], "skillId": [6840], "ishideUI": 1}, "10344": {"id": 10344, "name": "����", "rarity": 3, "desc": "<outline color=black width=2>������������<color=#FFDD42>+25%</color></outline>", "type": 3, "time": -1, "isOverlay": 3, "icon": "img/ModeBackpackHero/bufficon/skill_sdl", "attr": [148], "value": [[0.25]], "skillId": [6840], "ishideUI": 1}, "10345": {"id": 10345, "name": "����", "rarity": 3, "desc": "<outline color=black width=2>�����������˺�<color=#FFDD42>+100%</color></outline>", "type": 3, "time": -1, "isOverlay": 2, "icon": "img/ModeBackpackHero/bufficon/skill_sdl", "attr": [149], "value": [[1]], "skillId": [6840], "ishideUI": 1}, "10346": {"id": 10346, "name": "�ٶ�����", "rarity": 2, "desc": "<outline color=black width=2>��������ȴ<color=#FFDD42>-25%</color></outline>", "type": 3, "time": -1, "isOverlay": 2, "icon": "img/ModeBackpackHero/bufficon/skill_sdl", "attr": [103], "value": [[-0.25]], "skillId": [6840], "ishideUI": 1}, "10347": {"id": 10347, "name": "����ǿ��", "rarity": 3, "desc": "<outline color=black width=2>�������˺�<color=#FFDD42>+40%</color>��Ծ����<color=#FFDD42>+2</color></outline>", "type": 3, "time": -1, "isOverlay": 2, "icon": "img/ModeBackpackHero/bufficon/skill_sdl", "attr": [100, 153], "value": [[0.4], [4]], "skillId": [6840], "ishideUI": 1}, "10348": {"id": 10348, "name": "����", "rarity": 4, "desc": "<outline color=black width=2>������ÿ����Ծ�˺�����<color=#FFDD42>80%</color></outline>", "type": 3, "time": -1, "isOverlay": 10, "icon": "img/ModeBackpackHero/bufficon/skill_sdl", "attr": [100], "value": [[0.8]], "skillId": [6840], "ishideUI": 1, "className": "<PERSON><PERSON>_AtkAddLayerCleanAll", "otherValue": [1.3], "isSelect": 1}, "10349": {"id": 10349, "name": "����ֲ�", "rarity": 4, "desc": "<outline color=black width=2>����������<color=#FFDD42>+2</color></outline>", "type": 3, "time": -1, "icon": "img/ModeBackpackHero/bufficon/skill_sdl", "attr": [101], "value": [[2]], "skillId": [6840], "ishideUI": 1}, "10350": {"id": 10350, "name": "��������1", "rarity": 2, "desc": "<outline color=black width=2>��״��������+1</outline>", "type": 3, "time": -1, "icon": "img/ModeBackpackHero/bufficon/skill_chzhshd", "attr": [101], "value": [[1]], "skillId": [6860, 68600], "ishideUI": 1}, "10351": {"id": 10351, "name": "��������2", "rarity": 3, "desc": "<outline color=black width=2>��״��������+2</outline>", "type": 3, "time": -1, "icon": "img/ModeBackpackHero/bufficon/skill_chzhshd", "attr": [101], "value": [[2]], "skillId": [6860, 68600], "ishideUI": 1}, "10352": {"id": 10352, "name": "�˺�����1", "rarity": 2, "desc": "<outline color=black width=2>��״�����˺�<color=#FFDD42>+30%</color></outline>", "type": 3, "time": -1, "isOverlay": 20, "icon": "img/ModeBackpackHero/bufficon/skill_chzhshd", "attr": [100], "value": [[0.3]], "skillId": [6860, 68600], "ishideUI": 1}, "10353": {"id": 10353, "name": "�˺�����2", "rarity": 3, "desc": "<outline color=black width=2>��״�����˺�<color=#FFDD42>+60%</color></outline>", "type": 3, "time": -1, "isOverlay": 10, "icon": "img/ModeBackpackHero/bufficon/skill_chzhshd", "attr": [100], "value": [[0.6]], "skillId": [6860, 68600], "ishideUI": 1}, "10354": {"id": 10354, "name": "����", "rarity": 3, "desc": "<outline color=black width=2>��״���籩����<color=#FFDD42>+25%</color></outline>", "type": 3, "time": -1, "isOverlay": 3, "icon": "img/ModeBackpackHero/bufficon/skill_chzhshd", "attr": [148], "value": [[0.25]], "skillId": [6860, 68600], "ishideUI": 1}, "10355": {"id": 10355, "name": "����", "rarity": 3, "desc": "<outline color=black width=2>��״���籩���˺�<color=#FFDD42>+100%</color></outline>", "type": 3, "time": -1, "isOverlay": 2, "icon": "img/ModeBackpackHero/bufficon/skill_chzhshd", "attr": [149], "value": [[1]], "skillId": [6860, 68600], "ishideUI": 1}, "10356": {"id": 10356, "name": "�ٶ�����", "rarity": 2, "desc": "<outline color=black width=2>��״������ȴ<color=#FFDD42>-25%</color></outline>", "type": 3, "time": -1, "isOverlay": 2, "icon": "img/ModeBackpackHero/bufficon/skill_chzhshd", "attr": [103], "value": [[-0.25]], "skillId": [6860, 68600], "ishideUI": 1}, "10357": {"id": 10357, "name": "��������1", "rarity": 2, "desc": "<outline color=black width=2>��״�����ͷŴ���<color=#FFDD42>+1</color></outline>", "type": 3, "time": -1, "isOverlay": 4, "icon": "img/ModeBackpackHero/bufficon/skill_chzhshd", "attr": [108], "value": [[1]], "skillId": [6860, 68600], "ishideUI": 1}, "10358": {"id": 10358, "name": "��������2", "rarity": 3, "desc": "<outline color=black width=2>��״�����ͷŴ���<color=#FFDD42>+2</color></outline>", "type": 3, "time": -1, "isOverlay": 2, "icon": "img/ModeBackpackHero/bufficon/skill_chzhshd", "attr": [108], "value": [[2]], "skillId": [6860, 68600], "ishideUI": 1}, "10359": {"id": 10359, "name": "��״��͸", "rarity": 4, "desc": "<outline color=black width=2>��״�������<color=#FFDD42>+50%</color>�˺�<color=#FFDD42>+50</color>�ҶԴ����ĵ�λҲ������˺�</outline>", "type": 3, "time": -1, "icon": "img/ModeBackpackHero/bufficon/skill_chzhshd", "attr": [139], "value": [[68600]], "skillId": [6860, 68600], "ishideUI": 1}, "10360": {"id": 10360, "name": "�ɸ�����1", "rarity": 2, "desc": "<outline color=black width=2>���縫����+1</outline>", "type": 3, "time": -1, "isOverlay": 3, "icon": "img/ModeBackpackHero/bufficon/hatchet_skill", "attr": [101], "value": [[1]], "skillId": [6880], "ishideUI": 1}, "10361": {"id": 10361, "name": "�ɸ�����2", "rarity": 3, "desc": "<outline color=black width=2>���縫����+2</outline>", "type": 3, "time": -1, "isOverlay": 2, "icon": "img/ModeBackpackHero/bufficon/hatchet_skill", "attr": [101], "value": [[2]], "skillId": [6880], "ishideUI": 1}, "10362": {"id": 10362, "name": "�˺�����1", "rarity": 2, "desc": "<outline color=black width=2>���縫�˺�<color=#FFDD42>+20%</color></outline>", "type": 3, "time": -1, "isOverlay": 20, "icon": "img/ModeBackpackHero/bufficon/hatchet_skill", "attr": [100], "value": [[0.2]], "skillId": [6880, 68800], "ishideUI": 1}, "10363": {"id": 10363, "name": "�˺�����2", "rarity": 3, "desc": "<outline color=black width=2>���縫�˺�<color=#FFDD42>+80%</color></outline>", "type": 3, "time": -1, "isOverlay": 10, "icon": "img/ModeBackpackHero/bufficon/hatchet_skill", "attr": [100], "value": [[0.8]], "skillId": [6880, 68800], "ishideUI": 1}, "10364": {"id": 10364, "name": "����", "rarity": 3, "desc": "<outline color=black width=2>���縫������<color=#FFDD42>+25%</color></outline>", "type": 3, "time": -1, "isOverlay": 3, "icon": "img/ModeBackpackHero/bufficon/hatchet_skill", "attr": [148], "value": [[0.25]], "skillId": [6880, 68800], "ishideUI": 1}, "10365": {"id": 10365, "name": "����", "rarity": 3, "desc": "<outline color=black width=2>���縫�����˺�<color=#FFDD42>+100%</color></outline>", "type": 3, "time": -1, "isOverlay": 2, "icon": "img/ModeBackpackHero/bufficon/hatchet_skill", "attr": [149], "value": [[1]], "skillId": [6880, 68800], "ishideUI": 1}, "10366": {"id": 10366, "name": "������ת", "rarity": 2, "desc": "<outline color=black width=2>���縫����ʱ��<color=#FFDD42>+2s</color></outline>", "type": 3, "time": -1, "isOverlay": 2, "icon": "img/ModeBackpackHero/bufficon/hatchet_skill", "attr": [103, 128], "value": [[0.32], [2]], "skillId": [6880, 68800], "ishideUI": 1}, "10367": {"id": 10367, "name": "�޸�", "rarity": 3, "desc": "<outline color=black width=2>���縫���<color=#FFDD42>+100%</color>�˺�<color=#FFDD42>+80%</color></outline>", "type": 3, "time": -1, "isOverlay": 2, "icon": "img/ModeBackpackHero/bufficon/hatchet_skill", "attr": [102, 100], "value": [[1], [0.8]], "skillId": [6880, 68800], "ishideUI": 1}, "10368": {"id": 10368, "name": "����ն", "rarity": 4, "desc": "<outline color=black width=2>���縫ͷ��ת�ٶ�<color=#FFDD42>+200%</color>�˺�<color=#FFDD42>+150%</color></outline>", "type": 3, "time": -1, "icon": "img/ModeBackpackHero/bufficon/hatchet_skill", "attr": [133, 100], "value": [[2], [1.5]], "skillId": [6880], "ishideUI": 1}, "10369": {"id": 10369, "name": "�ɸ�", "rarity": 3, "desc": "<outline color=black width=2>���縫��ת���̻ᷢ��<color=#FFDD42>1��</color>�ɸ�</outline>", "type": 3, "time": -1, "icon": "img/ModeBackpackHero/bufficon/hatchet_skill", "attr": [122], "value": [[68800, 5, 3, 0]], "skillId": [6880], "ishideUI": 1}, "10370": {"id": 10370, "name": "��ʯ����1", "rarity": 2, "desc": "<outline color=black width=2>��ʯ����+1</outline>", "type": 3, "time": -1, "isOverlay": 3, "icon": "img/ModeBackpackHero/bufficon/skill_gsh", "attr": [101], "value": [[1]], "skillId": [6900], "ishideUI": 1}, "10371": {"id": 10371, "name": "��ʯ����2", "rarity": 3, "desc": "<outline color=black width=2>��ʯ����+2</outline>", "type": 3, "time": -1, "isOverlay": 2, "icon": "img/ModeBackpackHero/bufficon/skill_gsh", "attr": [101], "value": [[2]], "skillId": [6900], "ishideUI": 1}, "10372": {"id": 10372, "name": "�˺�����1", "rarity": 2, "desc": "<outline color=black width=2>��ʯ�˺�<color=#FFDD42>+50%</color></outline>", "type": 3, "time": -1, "isOverlay": 20, "icon": "img/ModeBackpackHero/bufficon/skill_gsh", "attr": [100], "value": [[0.5]], "skillId": [6900], "ishideUI": 1}, "10373": {"id": 10373, "name": "�˺�����2", "rarity": 3, "desc": "<outline color=black width=2>��ʯ�˺�<color=#FFDD42>+100%</color></outline>", "type": 3, "time": -1, "isOverlay": 10, "icon": "img/ModeBackpackHero/bufficon/skill_gsh", "attr": [100], "value": [[1]], "skillId": [6900], "ishideUI": 1}, "10374": {"id": 10374, "name": "����", "rarity": 3, "desc": "<outline color=black width=2>��ʯ������<color=#FFDD42>+25%</color></outline>", "type": 3, "time": -1, "isOverlay": 3, "icon": "img/ModeBackpackHero/bufficon/skill_gsh", "attr": [148], "value": [[0.25]], "skillId": [6900], "ishideUI": 1}, "10375": {"id": 10375, "name": "����", "rarity": 3, "desc": "<outline color=black width=2>��ʯ�����˺�<color=#FFDD42>+100%</color></outline>", "type": 3, "time": -1, "isOverlay": 2, "icon": "img/ModeBackpackHero/bufficon/skill_gsh", "attr": [149], "value": [[1]], "skillId": [6900], "ishideUI": 1}, "10376": {"id": 10376, "name": "�ٶ�����", "rarity": 2, "desc": "<outline color=black width=2>��ʯ��ȴ<color=#FFDD42>-20%</color></outline>", "type": 3, "time": -1, "isOverlay": 2, "icon": "img/ModeBackpackHero/bufficon/skill_gsh", "attr": [103], "value": [[-0.2]], "skillId": [6900], "ishideUI": 1}, "10377": {"id": 10377, "name": "��ʯ���", "rarity": 3, "desc": "<outline color=black width=2>��ʯ�����ڼ�ÿ���˺�����<color=#FFDD42>+20%</color>����<color=#FFDD42>200%</color></outline>", "type": 3, "time": -1, "isOverlay": 10, "icon": "img/ModeBackpackHero/bufficon/skill_gsh", "attr": [100], "value": [[0.2]], "skillId": [6900], "ishideUI": 1, "className": "<PERSON><PERSON>_AtkAddLayerCleanAll", "otherValue": [3], "isSelect": 1}, "10378": {"id": 10378, "name": "��ʯ����", "rarity": 4, "desc": "<outline color=black width=2>��ʯ�ƶ��ٶ�<color=#FFDD42>+200%</color>�˺����<color=#FFDD42>-50%</color></outline>", "type": 3, "time": -1, "icon": "img/ModeBackpackHero/bufficon/skill_gsh", "attr": [133, 105], "value": [[2], [-0.5]], "skillId": [6900], "ishideUI": 1}, "10379": {"id": 10379, "name": "ʯ��", "rarity": 3, "desc": "<outline color=black width=2>��ʯ���е�λ���и���ʯ��<color=#FFDD42>1s</color></outline>", "type": 3, "time": -1, "icon": "img/ModeBackpackHero/bufficon/skill_gsh", "attr": [109], "value": [[103790, 5, 3, 0]], "skillId": [6900], "ishideUI": 1}, "10380": {"id": 10380, "name": "��ʯ����1", "rarity": 2, "desc": "<outline color=black width=2>��ʯ����+1</outline>", "type": 3, "time": -1, "isOverlay": 3, "icon": "img/ModeBackpackHero/bufficon/skill_ysh", "attr": [101], "value": [[1]], "skillId": [6920], "ishideUI": 1}, "10381": {"id": 10381, "name": "��ʯ����2", "rarity": 3, "desc": "<outline color=black width=2>��ʯ����+2</outline>", "type": 3, "time": -1, "isOverlay": 2, "icon": "img/ModeBackpackHero/bufficon/skill_ysh", "attr": [101], "value": [[2]], "skillId": [6920], "ishideUI": 1}, "10382": {"id": 10382, "name": "�˺�����1", "rarity": 2, "desc": "<outline color=black width=2>��ʯ�˺�<color=#FFDD42>+25%</color></outline>", "type": 3, "time": -1, "isOverlay": 20, "icon": "img/ModeBackpackHero/bufficon/skill_ysh", "attr": [100], "value": [[0.25]], "skillId": [6920, 69200], "ishideUI": 1}, "10383": {"id": 10383, "name": "�˺�����2", "rarity": 3, "desc": "<outline color=black width=2>��ʯ�˺�<color=#FFDD42>+100%</color></outline>", "type": 3, "time": -1, "isOverlay": 10, "icon": "img/ModeBackpackHero/bufficon/skill_ysh", "attr": [100], "value": [[1]], "skillId": [6920, 69200], "ishideUI": 1}, "10384": {"id": 10384, "name": "����", "rarity": 3, "desc": "<outline color=black width=2>��ʯ������<color=#FFDD42>+25%</color></outline>", "type": 3, "time": -1, "isOverlay": 3, "icon": "img/ModeBackpackHero/bufficon/skill_ysh", "attr": [148], "value": [[0.25]], "skillId": [6920, 69200], "ishideUI": 1}, "10385": {"id": 10385, "name": "����", "rarity": 3, "desc": "<outline color=black width=2>��ʯ�����˺�<color=#FFDD42>+100%</color></outline>", "type": 3, "time": -1, "isOverlay": 2, "icon": "img/ModeBackpackHero/bufficon/skill_ysh", "attr": [149], "value": [[1]], "skillId": [6920, 69200], "ishideUI": 1}, "10386": {"id": 10386, "name": "������ʯ", "rarity": 4, "desc": "<outline color=black width=2>��ʯ����ʱ�и��ʶ����ͷ�1��<color=#FFDD42>�߶��˺�</color>�ľ�����ʯ</outline>", "type": 3, "time": -1, "icon": "img/ModeBackpackHero/bufficon/skill_ysh", "attr": [122], "value": [[69200, 5, 3, 0.6, 0.2]], "skillId": [6920], "ishideUI": 1}, "10387": {"id": 10387, "name": "���", "rarity": 3, "desc": "<outline color=black width=2>��ʯ������<color=#FFDD42>+15%</color>�˺�<color=#FFDD42>+100%</color></outline>", "type": 3, "time": -1, "isOverlay": 2, "icon": "img/ModeBackpackHero/bufficon/skill_ysh", "attr": [148, 100], "value": [[0.15], [100]], "skillId": [6920, 69200], "ishideUI": 1}, "10388": {"id": 10388, "name": "��ʯ��", "rarity": 4, "desc": "<outline color=black width=2>��ʯ��ȴ<color=#FFDD42>-50%</color>����<color=#FFDD42>+5</color></outline>", "type": 3, "time": -1, "icon": "img/ModeBackpackHero/bufficon/skill_ysh", "attr": [103, 101], "value": [[-0.5], [5]], "skillId": [6920], "ishideUI": 1}, "10389": {"id": 10389, "name": "ȼ��", "rarity": 3, "desc": "<outline color=black width=2>��ʯ���е�λ���ڵ��ϲ���<color=#FFDD42>ȼ��</color></outline>", "type": 3, "time": -1, "icon": "img/ModeBackpackHero/bufficon/skill_ysh", "attr": [122], "value": [[69220, 5, 3, 0]], "skillId": [6920, 69200], "ishideUI": 1}, "10390": {"id": 10390, "name": "��ʸ����1", "rarity": 2, "desc": "<outline color=black width=2>���м�ʸ����+1</outline>", "type": 3, "time": -1, "isOverlay": 4, "icon": "v1/images/equip/icon_arrow", "attr": [101], "value": [[1]], "skillId": [6780, 67800, 67820], "ishideUI": 1}, "10391": {"id": 10391, "name": "��ʸ����2", "rarity": 3, "desc": "<outline color=black width=2>���м�ʸ����+2</outline>", "type": 3, "time": -1, "isOverlay": 3, "icon": "v1/images/equip/icon_arrow", "attr": [101], "value": [[2]], "skillId": [6780, 67800, 67820], "ishideUI": 1}, "10392": {"id": 10392, "name": "�˺�����1", "rarity": 2, "desc": "<outline color=black width=2>���м�ʸ�˺�<color=#FFDD42>+50%</color></outline>", "type": 3, "time": -1, "isOverlay": 20, "icon": "v1/images/equip/icon_arrow", "attr": [100], "value": [[0.5]], "skillId": [6780, 67800, 67820], "ishideUI": 1}, "10393": {"id": 10393, "name": "�˺�����2", "rarity": 3, "desc": "<outline color=black width=2>���м�ʸ�˺�<color=#FFDD42>+100%</color></outline>", "type": 3, "time": -1, "isOverlay": 10, "icon": "v1/images/equip/icon_arrow", "attr": [100], "value": [[1]], "skillId": [6780, 67800, 67820], "ishideUI": 1}, "10394": {"id": 10394, "name": "����", "rarity": 2, "desc": "<outline color=black width=2>���м�ʸ������<color=#FFDD42>+15%</color></outline>", "type": 3, "time": -1, "isOverlay": 4, "icon": "v1/images/equip/icon_arrow", "attr": [148], "value": [[0.15]], "skillId": [6780, 67800, 67820], "ishideUI": 1}, "10395": {"id": 10395, "name": "����", "rarity": 3, "desc": "<outline color=black width=2>���м�ʸ�����˺�<color=#FFDD42>+100%</color></outline>", "type": 3, "time": -1, "isOverlay": 2, "icon": "v1/images/equip/icon_arrow", "attr": [149], "value": [[1]], "skillId": [6780, 67800, 67820], "ishideUI": 1}, "10396": {"id": 10396, "name": "������1��", "rarity": 4, "desc": "<outline color=black width=2>ÿ�ι����и��ʽ���������1��״̬<color=#FFDD42>����������</color>����2s</outline>", "type": 3, "time": -1, "icon": "v1/images/equip/icon_arrow", "attr": [143], "value": [[67840]], "skillId": [6780, 67800, 67820], "ishideUI": 1}, "10397": {"id": 10397, "name": "������2��", "rarity": 4, "desc": "<outline color=black width=2>ÿ�ι����и��ʽ���������2����̬,�����ض�<color=#FFDD42>����</color>,��͸��<color=#FFDD42>+2</color>,����2s</outline>", "type": 3, "time": -1, "icon": "v1/images/equip/icon_arrow", "attr": [143], "value": [[67860]], "skillId": [6780, 67800, 67820], "ishideUI": 1}, "10398": {"id": 10398, "name": "������", "rarity": 3, "desc": "<outline color=black width=2>���ⷢ��ɴ�͸�ĺ�����ʸ,���е�λ�����<color=#FFDD42>40%</color>����2s</outline>", "type": 3, "time": -1, "icon": "v1/images/equip/icon_arrow", "attr": [143], "value": [[67800]], "skillId": [6780], "ishideUI": 1}, "10399": {"id": 10399, "name": "�����", "rarity": 3, "desc": "<outline color=black width=2>���ⷢ������ʸ,���е�λ�����<color=#FFDD42>ȼ��</color></outline>", "type": 3, "time": -1, "icon": "v1/images/equip/icon_arrow", "attr": [143], "value": [[67820]], "skillId": [6780], "ishideUI": 1}, "10400": {"id": 10400, "name": "ӥ֮ӡ��", "rarity": 3, "desc": "<outline color=black width=2>�������еĵ�λ�ᱻ���ʹ���ܵ��˺�<color=#FFDD42>+30%</color>����2s</outline>", "type": 3, "time": -1, "icon": "v1/images/equip/icon_arrow", "attr": [109], "value": [[104000, 5, 3, 0]], "skillId": [6780], "ishideUI": 1}, "10401": {"id": 10401, "name": "��׼���", "rarity": 3, "desc": "<outline color=black width=2>�������ʱ��10%����,�������<color=#FFDD42>100%</color>�˺�,����2s</outline>", "type": 3, "time": -1, "icon": "v1/images/equip/icon_arrow", "attr": [109], "value": [[104010, 5, 1, 0]], "skillId": [6780, 67800, 67820], "ishideUI": 1}, "10402": {"id": 10402, "name": "����", "rarity": 2, "desc": "<outline color=black width=2>���м�ʸ��ȴ <color=#FFDD42>-15%</color></outline>", "type": 3, "time": -1, "isOverlay": 2, "icon": "v1/images/equip/icon_arrow", "attr": [103], "value": [[-0.15]], "skillId": [6780, 67800, 67820], "ishideUI": 1}, "10410": {"id": 10410, "name": "��ϻ1", "rarity": 2, "desc": "<outline color=black width=2>����ǹ��ϻ����+1</outline>", "type": 3, "time": -1, "isOverlay": 3, "icon": "v1/images/equip/item_20011", "attr": [101], "value": [[1]], "skillId": [6800], "ishideUI": 1}, "10411": {"id": 10411, "name": "��ϻ2", "rarity": 3, "desc": "<outline color=black width=2>����ǹ��ϻ����+3</outline>", "type": 3, "time": -1, "isOverlay": 4, "icon": "v1/images/equip/item_20011", "attr": [101], "value": [[3]], "skillId": [6800], "ishideUI": 1}, "10412": {"id": 10412, "name": "�˺�����1", "rarity": 2, "desc": "<outline color=black width=2>����ǹ�˺�<color=#FFDD42>+50%</color></outline>", "type": 3, "time": -1, "isOverlay": 20, "icon": "v1/images/equip/item_20011", "attr": [100], "value": [[0.5]], "skillId": [6800, 68000], "ishideUI": 1}, "10413": {"id": 10413, "name": "�˺�����2", "rarity": 3, "desc": "<outline color=black width=2>����ǹ�˺�<color=#FFDD42>+100%</color></outline>", "type": 3, "time": -1, "isOverlay": 10, "icon": "v1/images/equip/item_20011", "attr": [100], "value": [[1]], "skillId": [6800, 68000], "ishideUI": 1}, "10414": {"id": 10414, "name": "����", "rarity": 3, "desc": "<outline color=black width=2>����ǹ������<color=#FFDD42>+25%</color></outline>", "type": 3, "time": -1, "isOverlay": 3, "icon": "v1/images/equip/item_20011", "attr": [148], "value": [[0.25]], "skillId": [6800], "ishideUI": 1}, "10415": {"id": 10415, "name": "����", "rarity": 3, "desc": "<outline color=black width=2>����ǹ�����˺�<color=#FFDD42>+100%</color></outline>", "type": 3, "time": -1, "isOverlay": 2, "icon": "v1/images/equip/item_20011", "attr": [149], "value": [[1]], "skillId": [6800, 68000], "ishideUI": 1}, "10416": {"id": 10416, "name": "��ɱ", "rarity": 3, "desc": "<outline color=black width=2>����ǹ�ӵ���ɱ��λ�ĸ���<color=#FFDD42>+1%</color></outline>", "type": 3, "time": -1, "isOverlay": 5, "icon": "v1/images/equip/item_20011", "attr": [138], "value": [[0.005]], "skillId": [6800], "ishideUI": 1, "buffId": [104160]}, "10417": {"id": 10417, "name": "�������", "rarity": 3, "desc": "<outline color=black width=2>����ǹ��ϻ��պ��и�������<color=#FFDD42>����</color>��ϻ</outline>", "type": 3, "time": -1, "icon": "v1/images/equip/item_20011", "attr": [109], "value": [[104170, 7, 1, 0]], "skillId": [6800], "ishideUI": 1}, "10418": {"id": 10418, "name": "Բ����", "rarity": 4, "desc": "<outline color=black width=2>����ϻ��պ��и���360�ȷ���ض�<color=#FFDD42>����</color>���ӵ�</outline>", "type": 3, "time": -1, "icon": "v1/images/equip/item_20011", "attr": [122], "value": [[68000, 7, 1, 0, 0.3]], "skillId": [6800], "ishideUI": 1}, "10419": {"id": 10419, "name": "����", "rarity": 4, "desc": "<outline color=black width=2>ÿ���ӵ�����<color=#FFDD42>+1</color></outline>", "type": 3, "time": -1, "isOverlay": 2, "icon": "v1/images/equip/item_20011", "attr": [500, 120], "value": [[1], [1]], "skillId": [6800, 68000], "ishideUI": 1}, "10420": {"id": 10420, "name": "�����鷢", "rarity": 4, "desc": "<outline color=black width=2>��ϻ�ڵ�ÿ���ӵ��˺�����<color=#FFDD42>15%</color></outline>", "type": 3, "time": -1, "isOverlay": 18, "icon": "v1/images/equip/item_20011", "attr": [100], "value": [[0.15]], "skillId": [6800], "ishideUI": 1, "className": "<PERSON><PERSON>_AtkAddLayerCleanAll", "otherValue": [1.1], "isSelect": 1}, "10421": {"id": 10421, "name": "����װ��", "rarity": 2, "desc": "<outline color=black width=2>����ǹ�ٶ�<color=#FFDD42>+15%</color></outline>", "type": 3, "time": -1, "isOverlay": 2, "icon": "v1/images/equip/item_20011", "attr": [103], "value": [[-0.15]], "skillId": [6800, 68000], "ishideUI": 1}, "10430": {"id": 10430, "name": "����", "rarity": 4, "desc": "<outline color=black width=2>��ʦ���з�����������<color=#FFDD42>+1</color></outline>", "type": 3, "time": -1, "icon": "v1/images/equip/icon_Staff", "attr": [108], "value": [[1]], "skillId": [6820, 68200, 68220], "ishideUI": 1}, "10431": {"id": 10431, "name": "�����䷨", "rarity": 3, "desc": "<outline color=black width=2>��ʦ���з����ٶ�<color=#FFDD42>+15%</color></outline>", "type": 3, "time": -1, "isOverlay": 2, "icon": "v1/images/equip/icon_Staff", "attr": [103], "value": [[-0.15]], "skillId": [6820, 68200, 68220], "ishideUI": 1}, "10432": {"id": 10432, "name": "�����˺�1", "rarity": 2, "desc": "<outline color=black width=2>��ʦ���з����˺�<color=#FFDD42>+30%</color></outline>", "type": 3, "time": -1, "isOverlay": 20, "icon": "v1/images/equip/icon_Staff", "attr": [100], "value": [[0.3]], "skillId": [6820, 68200, 68220, 682000, 682200, 68240], "ishideUI": 1}, "10433": {"id": 10433, "name": "�����˺�2", "rarity": 3, "desc": "<outline color=black width=2>��ʦ���з����˺�<color=#FFDD42>+70%</color></outline>", "type": 3, "time": -1, "isOverlay": 10, "icon": "v1/images/equip/icon_Staff", "attr": [100], "value": [[0.7]], "skillId": [6820, 68200, 68220, 682000, 682200, 68240], "ishideUI": 1}, "10434": {"id": 10434, "name": "����", "rarity": 3, "desc": "<outline color=black width=2>��ʦ���з���������<color=#FFDD42>+25%</color></outline>", "type": 3, "time": -1, "isOverlay": 2, "icon": "v1/images/equip/icon_Staff", "attr": [148], "value": [[0.25]], "skillId": [6820, 68200, 68220, 682000, 682200, 68240], "ishideUI": 1}, "10435": {"id": 10435, "name": "����", "rarity": 3, "desc": "<outline color=black width=2>��ʦ���з��������˺�<color=#FFDD42>+100%</color></outline>", "type": 3, "time": -1, "isOverlay": 2, "icon": "v1/images/equip/icon_Staff", "attr": [149], "value": [[1]], "skillId": [6820, 68200, 68220, 682000, 682200, 68240], "ishideUI": 1}, "10436": {"id": 10436, "name": "��������", "rarity": 3, "desc": "<outline color=black width=2>���<color=#FFDD42>��������</color></outline>", "type": 3, "time": -1, "icon": "v1/images/equip/icon_Staff", "attr": [143], "value": [[68200]], "skillId": [6820], "ishideUI": 1}, "10437": {"id": 10437, "name": "��������", "rarity": 4, "desc": "<outline color=black width=2>��������ÿ�����ж��и��ʲ����µ�<color=#FFDD42>��������</color></outline>", "type": 3, "time": -1, "icon": "v1/images/equip/icon_Staff", "attr": [122], "value": [[682000, 5, 3, 0]], "skillId": [68200], "ishideUI": 1}, "10438": {"id": 10438, "name": "�ŷ�", "rarity": 3, "desc": "<outline color=black width=2>������������<color=#FFDD42>+3</color></outline>", "type": 3, "time": -1, "isOverlay": 2, "icon": "v1/images/equip/icon_Staff", "attr": [101], "value": [[3]], "skillId": [68200], "ishideUI": 1}, "10439": {"id": 10439, "name": "��������", "rarity": 3, "desc": "<outline color=black width=2>���<color=#FFDD42>��������</color></outline>", "type": 3, "time": -1, "icon": "v1/images/equip/icon_Staff", "attr": [143], "value": [[68220]], "skillId": [6820], "ishideUI": 1}, "10440": {"id": 10440, "name": "��������", "rarity": 4, "desc": "<outline color=black width=2>���а����������к��и��ʷ���<color=#FFDD42>��������</color></outline>", "type": 3, "time": -1, "icon": "v1/images/equip/icon_Staff", "attr": [122], "value": [[68240, 5, 3, 0, 0.4]], "skillId": [68220, 682200], "ishideUI": 1}, "10441": {"id": 10441, "name": "��������", "rarity": 3, "desc": "<outline color=black width=2>��������ÿ�ε��䶼���Է���2��<color=#FFDD42>��������</color></outline>", "type": 3, "time": -1, "isOverlay": 2, "icon": "v1/images/equip/icon_Staff", "attr": [122], "value": [[682200, 5, 3, 0]], "skillId": [68220], "ishideUI": 1}, "10442": {"id": 10442, "name": "��������", "rarity": 3, "desc": "<outline color=black width=2>���з�������<color=#FFDD42>+1</color></outline>", "type": 3, "time": -1, "isOverlay": 2, "icon": "v1/images/equip/icon_Staff", "attr": [101], "value": [[1]], "skillId": [6820, 68200, 68220], "ishideUI": 1}, "10443": {"id": 10443, "name": "��ȼ", "rarity": 2, "desc": "<outline color=black width=2>�������п�<color=#FFDD42>��ȼ</color>��λ,����3s</outline>", "type": 3, "time": -1, "icon": "v1/images/equip/icon_Staff", "attr": [109], "value": [[1044300, 5, 3, 0]], "skillId": [6820], "ishideUI": 1}, "10444": {"id": 10444, "name": "��������", "rarity": 2, "desc": "<outline color=black width=2>��������<color=#FFDD42>+1</color></outline>", "type": 3, "time": -1, "isOverlay": 3, "icon": "v1/images/equip/icon_Staff", "attr": [101], "value": [[1]], "skillId": [6820], "ishideUI": 1}, "10445": {"id": 10445, "name": "����͸", "rarity": 2, "desc": "<outline color=black width=2>����͸����<color=#FFDD42>+1</color></outline>", "type": 3, "time": -1, "isOverlay": 2, "icon": "v1/images/equip/icon_Staff", "attr": [120], "value": [[1]], "skillId": [6820], "ishideUI": 1}, "10446": {"id": 10446, "name": "�����", "rarity": 4, "desc": "<outline color=black width=2>�������<color=#FFDD42>+100%</color>,�˺�<color=#FFDD42>+200%</color>,��͸<color=#FFDD42>+3</color></outline>", "type": 3, "time": -1, "icon": "v1/images/equip/icon_Staff", "attr": [102, 100, 120], "value": [[1], [2], [3]], "skillId": [6820], "ishideUI": 1}, "99970": {"id": 99970, "name": "�����ܼ���", "rarity": 3, "desc": "<outline color=black width=2>����10%</outline>", "type": 1, "time": 3, "attr": [8], "value": [[1.2]], "weight": 0.7, "ishideUI": 1}, "99971": {"id": 99971, "name": "�����ܼ���", "rarity": 3, "desc": "<outline color=black width=2>����10%</outline>", "type": 1, "time": 10, "attr": [8], "value": [[1]], "weight": 0.2, "ishideUI": 1}, "100000": {"id": 100000, "name": "ȫ���˺�1", "rarity": 2, "desc": "<outline color=black width=2>ȫ���˺�<color=#FFDD42>+20%</color></outline>", "type": 1, "time": -1, "icon": "img/ModeBackpackHero/bufficon/attckup", "attr": [3], "value": [[0.2]], "ishideUI": 1}, "100001": {"id": 100001, "name": "ȫ���˺�1", "rarity": 3, "desc": "<outline color=black width=2>ȫ���˺�<color=#FFDD42>+50%</color></outline>", "type": 1, "time": -1, "icon": "img/ModeBackpackHero/bufficon/attckup", "attr": [3], "value": [[0.5]], "ishideUI": 1}, "100002": {"id": 100002, "name": "ȫ���˺�2", "rarity": 4, "desc": "<outline color=black width=2>ȫ���˺�<color=#FFDD42>+100%</color></outline>", "type": 1, "time": -1, "icon": "img/ModeBackpackHero/bufficon/attckup", "attr": [3], "value": [[1]], "ishideUI": 1}, "100003": {"id": 100003, "name": "ȫ���ٶ�1", "rarity": 2, "desc": "<outline color=black width=2>���м����ٶ�<color=#FFDD42>+5%</color></outline>", "type": 3, "time": -1, "icon": "img/ModeBackpackHero/bufficon/equipcd", "attr": [103], "value": [[-0.05]], "ishideUI": 1}, "100004": {"id": 100004, "name": "ȫ���ٶ�2", "rarity": 3, "desc": "<outline color=black width=2>���м����ٶ�<color=#FFDD42>+10%</color></outline>", "type": 3, "time": -1, "icon": "img/ModeBackpackHero/bufficon/equipcd", "attr": [103], "value": [[-0.1]], "ishideUI": 1}, "100005": {"id": 100005, "name": "ȫ���ٶ�3", "rarity": 4, "desc": "<outline color=black width=2>���м����ٶ�<color=#FFDD42>+15%</color></outline>", "type": 3, "time": -1, "icon": "img/ModeBackpackHero/bufficon/equipcd", "attr": [103], "value": [[-0.15]], "ishideUI": 1}, "100006": {"id": 100006, "name": "ȫ�ֵ���", "rarity": 3, "desc": "<outline color=black width=2>���м��ܵ�������<color=#FFDD42>+1</color></outline>", "type": 3, "time": -1, "icon": "img/ModeBackpackHero/bufficon/skill_qjdd", "attr": [101], "value": [[1]], "ishideUI": 1}, "100007": {"id": 100007, "name": "ȫ�ֵ���", "rarity": 4, "desc": "<outline color=black width=2>���м��ܵ�������<color=#FFDD42>+2</color></outline>", "type": 3, "time": -1, "icon": "img/ModeBackpackHero/bufficon/skill_qjdd", "attr": [101], "value": [[2]], "ishideUI": 1}, "100008": {"id": 100008, "name": "������1", "rarity": 2, "desc": "<outline color=black width=2>���ĳ�ʼ����<color=#FFDD42>-20%</color></outline>", "type": 3, "time": -1, "isOverlay": 1, "icon": "img/ModeBackpackHero/bufficon/bufficon_dragonspeed", "attr": [8], "value": [[-0.2]], "ishideUI": 1}, "100009": {"id": 100009, "name": "������2", "rarity": 3, "desc": "<outline color=black width=2>���ĳ�ʼ����<color=#FFDD42>-30%</color></outline>", "type": 3, "time": -1, "isOverlay": 1, "icon": "img/ModeBackpackHero/bufficon/bufficon_dragonspeed", "attr": [8], "value": [[-0.3]], "ishideUI": 1}, "100010": {"id": 100010, "name": "����1", "rarity": 2, "desc": "<outline color=black width=2>���񱩸��ʽ���<color=#FFDD42>15%</color></outline>", "type": 3, "time": -1, "isOverlay": 1, "icon": "img/ModeBackpackHero/bufficon/bufficon_dragoncrazy", "attr": [138], "value": [[-0.15]], "ishideUI": 1, "object": 1, "className": "<PERSON><PERSON>_<PERSON><PERSON>", "effectArea": 3000}, "100011": {"id": 100011, "name": "����2", "rarity": 3, "desc": "<outline color=black width=2>���񱩸��ʽ���<color=#FFDD42>30%</color></outline>", "type": 3, "time": -1, "isOverlay": 1, "icon": "img/ModeBackpackHero/bufficon/bufficon_dragoncrazy", "attr": [138], "value": [[-0.3]], "ishideUI": 1, "object": 1, "className": "<PERSON><PERSON>_<PERSON><PERSON>", "effectArea": 3000}, "100012": {"id": 100012, "name": "����1", "rarity": 3, "desc": "<outline color=black width=2>�����������ɫ�ͽ�ɫ�����ĸ���<color=#FFDD42>+5%</color></outline>", "type": 3, "time": -1, "icon": "img/ModeBackpackHero/bufficon/skill_star", "attr": [138], "value": [[8]], "skillId": [0], "ishideUI": 1}, "100013": {"id": 100013, "name": "����2", "rarity": 4, "desc": "<outline color=black width=2>�����������ɫ�ͽ�ɫ�����ĸ���<color=#FFDD42>+10%</color></outline>", "type": 3, "time": -1, "icon": "img/ModeBackpackHero/bufficon/skill_star", "attr": [138], "value": [[15]], "skillId": [0], "ishideUI": 1}, "100041": {"id": 100041, "name": "����2�����1��", "rarity": 3, "desc": "<outline color=black width=2>����֮�����1��</outline>", "type": 3, "time": -1, "icon": "img/ModeBackpackHero/bufficon/bsshdj", "attr": [501], "value": [[1]], "skillId": [6080]}, "100042": {"id": 100042, "name": "������������", "rarity": 3, "desc": "<outline color=black width=2>����֮������2</outline>", "type": 3, "time": 0.8, "isOverlay": 5, "icon": "img/ModeBackpackHero/bufficon/bsshdj", "attr": [100], "value": [[0.25]], "skillId": [6080], "isSelect": 1}, "100050": {"id": 100050, "name": "����֮�л���", "rarity": 2, "type": 6, "time": 3, "attr": [124], "value": [[40]], "res": ["bones/skill/fx_burn", "2"], "ishideUI": 1, "object": 1, "className": "B<PERSON>_<PERSON>", "otherValue": [0.5]}, "100070": {"id": 100070, "name": "�����˺�����", "rarity": 3, "desc": "<outline color=black width=2>�ܵ��˺�+15%</outline>", "type": 2, "time": 3, "isOverlay": 3, "icon": "v1/images/equip/item_20145", "attr": [127], "value": [[0.2]], "res": ["bones/skill/fx_seriousinjury", "2"]}, "100100": {"id": 100100, "name": "�綾֮���ж��˺�", "rarity": 2, "type": 6, "time": 3, "isOverlay": 5, "attr": [124], "value": [[10]], "res": ["bones/skill/fx_poisoning", "2"], "ishideUI": 1, "object": 1, "className": "B<PERSON>_<PERSON>", "otherValue": [0.3]}, "100170": {"id": 100170, "name": "�ٶ�", "rarity": 3, "desc": "<outline color=black width=2>���ƽ����к����ʹ��λ����<color=#FFDD42>10%</color>����3s�ɵ���4��</outline>", "type": 3, "time": 3, "isOverlay": 4, "icon": "v1/images/equip/item_20145", "attr": [8], "value": [[-0.1]], "ishideUI": 1}, "100320": {"id": 100320, "name": "��������", "rarity": 2, "type": 6, "time": 10, "attr": [124], "value": [[50]], "res": ["bones/skill/fx_burn_black", "2"], "ishideUI": 1, "object": 1, "className": "B<PERSON>_<PERSON>", "otherValue": [0.25]}, "100910": {"id": 100910, "name": "�𹿰�����", "rarity": 2, "type": 6, "time": 2, "attr": [124], "value": [[50]], "res": ["bones/skill/fx_burn", "2"], "ishideUI": 1, "object": 1, "className": "B<PERSON>_<PERSON>", "otherValue": [0.25]}, "101030": {"id": 101030, "name": "����֮��2", "rarity": 4, "desc": "<outline color=black width=2>���޷���ʱ��<color=#FFDD42>Խ��Խ��</color>,�˺�Ҳ����<color=#FFDD42>����</color>���и���<color=#FFDD42>����</color>ʯ���ĵ�λ</outline>", "type": 1, "time": -1, "isOverlay": 15, "icon": "v1/images/equip/item_20045", "attr": [13], "value": [[0.025]], "ishideUI": 1, "className": "Buff_OnTime", "otherValue": [0.5]}, "101031": {"id": 101031, "name": "����֮��2", "rarity": 4, "desc": "<outline color=black width=2>���޷���ʱ��<color=#FFDD42>Խ��Խ��</color>,�˺�Ҳ����<color=#FFDD42>����</color>���и���<color=#FFDD42>����</color>ʯ���ĵ�λ</outline>", "type": 1, "time": -1, "isOverlay": 15, "icon": "v1/images/equip/item_20045", "attr": [3], "value": [[0.05]], "ishideUI": 1, "className": "Buff_OnTime", "otherValue": [0.5]}, "101190": {"id": 101190, "name": "����", "rarity": 3, "desc": "<outline color=black width=2>��֮�����к����ʹ��λ����<color=#FFDD42>25%</color>����2s</outline>", "type": 2, "time": 2, "icon": "v1/images/equip/item_20145", "attr": [8], "value": [[-0.2]], "ishideUI": 1}, "102500": {"id": 102500, "name": "�����˺�1", "rarity": 2, "desc": "<b><outline color=black width=2>�����˺�<color=#FFDD42>+60</color></outline></b>", "type": 3, "time": -1, "isOverlay": 99, "icon": "v1/images/equip/icon_shotgun", "attr": [100], "value": [[60]], "skillId": [9060], "ishideUI": 1}, "102501": {"id": 102501, "name": "�����˺�2", "rarity": 3, "desc": "<b><outline color=black width=2>�����˺�<color=#FFDD42>+120</color></outline></b>", "type": 3, "time": -1, "isOverlay": 99, "icon": "v1/images/equip/icon_shotgun", "attr": [100], "value": [[120]], "skillId": [9060], "ishideUI": 1}, "102502": {"id": 102502, "name": "�����˺�3", "rarity": 4, "desc": "<b><outline color=black width=2>�����˺�<color=#FFDD42>+240</color></outline></b>", "type": 3, "time": -1, "isOverlay": 99, "icon": "v1/images/equip/icon_shotgun", "attr": [100], "value": [[240]], "skillId": [9060], "ishideUI": 1}, "102503": {"id": 102503, "name": "��������1", "rarity": 2, "desc": "<b><outline color=black width=2>��������<color=#FFDD42>+10%</color></outline></b>", "type": 3, "time": -1, "isOverlay": 99, "icon": "v1/images/equip/icon_shotgun", "attr": [103, 133], "value": [[-0.1], [0.1]], "skillId": [9060], "ishideUI": 1}, "102504": {"id": 102504, "name": "��������2", "rarity": 3, "desc": "<b><outline color=black width=2>��������<color=#FFDD42>+20%</color></outline></b>", "type": 3, "time": -1, "isOverlay": 99, "icon": "v1/images/equip/icon_shotgun", "attr": [103, 133], "value": [[-0.2], [0.1]], "skillId": [9060], "ishideUI": 1}, "102505": {"id": 102505, "name": "��������3", "rarity": 4, "desc": "<b><outline color=black width=2>��������<color=#FFDD42>+50%</color></outline></b>", "type": 3, "time": -1, "isOverlay": 99, "icon": "v1/images/equip/icon_shotgun", "attr": [103, 133], "value": [[-0.5], [0.1]], "skillId": [9060], "ishideUI": 1}, "102506": {"id": 102506, "name": "��������1", "rarity": 2, "desc": "<b><outline color=black width=2>��������<color=#FFDD42>+1</color></outline></b>", "type": 3, "time": -1, "isOverlay": 99, "icon": "v1/images/equip/icon_shotgun", "attr": [101], "value": [[1]], "skillId": [9060], "ishideUI": 1}, "102507": {"id": 102507, "name": "��������2", "rarity": 3, "desc": "<b><outline color=black width=2>��������<color=#FFDD42>+2</color></outline></b>", "type": 3, "time": -1, "isOverlay": 99, "icon": "v1/images/equip/icon_shotgun", "attr": [101], "value": [[2]], "skillId": [9060], "ishideUI": 1}, "102508": {"id": 102508, "name": "��������3", "rarity": 4, "desc": "<b><outline color=black width=2>��������<color=#FFDD42>+3</color></outline></b>", "type": 3, "time": -1, "isOverlay": 99, "icon": "v1/images/equip/icon_shotgun", "attr": [101], "value": [[3]], "skillId": [9060], "ishideUI": 1}, "102509": {"id": 102509, "name": "������͸1", "rarity": 2, "desc": "<b><outline color=black width=2>������͸<color=#FFDD42>+1</color></outline></b>", "type": 3, "time": -1, "isOverlay": 99, "icon": "v1/images/equip/icon_shotgun", "attr": [120], "value": [[1]], "skillId": [9060], "ishideUI": 1}, "102510": {"id": 102510, "name": "������͸2", "rarity": 3, "desc": "<b><outline color=black width=2>������͸<color=#FFDD42>+2</color></outline></b>", "type": 3, "time": -1, "isOverlay": 99, "icon": "v1/images/equip/icon_shotgun", "attr": [120], "value": [[2]], "skillId": [9060], "ishideUI": 1}, "102511": {"id": 102511, "name": "������͸3", "rarity": 4, "desc": "<b><outline color=black width=2>������͸<color=#FFDD42>+5</color></outline></b>", "type": 3, "time": -1, "isOverlay": 99, "icon": "v1/images/equip/icon_shotgun", "attr": [120], "value": [[3]], "skillId": [9060], "ishideUI": 1}, "102512": {"id": 102512, "name": "��������1", "rarity": 2, "desc": "<b><outline color=black width=2>��������<color=#FFDD42>+10%</color></outline></b>", "type": 3, "time": -1, "isOverlay": 99, "icon": "v1/images/equip/icon_shotgun", "attr": [148], "value": [[0.1]], "skillId": [9060], "ishideUI": 1}, "102513": {"id": 102513, "name": "��������2", "rarity": 3, "desc": "<b><outline color=black width=2>��������<color=#FFDD42>+25%</color></outline></b>", "type": 3, "time": -1, "isOverlay": 99, "icon": "v1/images/equip/icon_shotgun", "attr": [148], "value": [[0.25]], "skillId": [9060], "ishideUI": 1}, "102514": {"id": 102514, "name": "��������3", "rarity": 4, "desc": "<b><outline color=black width=2>��������<color=#FFDD42>+40%</color></outline></b>", "type": 3, "time": -1, "isOverlay": 99, "icon": "v1/images/equip/icon_shotgun", "attr": [148], "value": [[0.4]], "skillId": [9060], "ishideUI": 1}, "102515": {"id": 102515, "name": "��������1", "rarity": 2, "desc": "<b><outline color=black width=2>��������<color=#FFDD42>+25%</color></outline></b>", "type": 3, "time": -1, "isOverlay": 99, "icon": "v1/images/equip/icon_shotgun", "attr": [149], "value": [[0.25]], "skillId": [9060], "ishideUI": 1}, "102516": {"id": 102516, "name": "��������2", "rarity": 3, "desc": "<b><outline color=black width=2>��������<color=#FFDD42>+50%</color></outline></b>", "type": 3, "time": -1, "isOverlay": 99, "icon": "v1/images/equip/icon_shotgun", "attr": [149], "value": [[0.5]], "skillId": [9060], "ishideUI": 1}, "102517": {"id": 102517, "name": "��������3", "rarity": 4, "desc": "<b><outline color=black width=2>��������<color=#FFDD42>+100%</color></outline></b>", "type": 3, "time": -1, "isOverlay": 99, "icon": "v1/images/equip/icon_shotgun", "attr": [149], "value": [[1]], "skillId": [9060], "ishideUI": 1}, "102518": {"id": 102518, "name": "������", "rarity": 4, "desc": "<b><outline color=black width=2>��������3���˺�<color=#FFDD42>+100%</color></outline></b>", "type": 3, "time": -1, "isOverlay": 99, "icon": "v1/images/equip/icon_shotgun", "attr": [100], "value": [[1]], "skillId": [9060], "ishideUI": 1}, "102600": {"id": 102600, "name": "�����˺�1", "rarity": 2, "desc": "<b><outline color=black width=2>�����˺�<color=#FFDD42>+20</color></outline></b>", "type": 3, "time": -1, "isOverlay": 99, "icon": "v1/images/equip/icon_dragonball", "attr": [100], "value": [[20]], "skillId": [9040], "ishideUI": 1}, "102601": {"id": 102601, "name": "�����˺�2", "rarity": 3, "desc": "<b><outline color=black width=2>�����˺�<color=#FFDD42>+30</color></outline></b>", "type": 3, "time": -1, "isOverlay": 99, "icon": "v1/images/equip/icon_dragonball", "attr": [100], "value": [[30]], "skillId": [9040], "ishideUI": 1}, "102602": {"id": 102602, "name": "�����˺�3", "rarity": 4, "desc": "<b><outline color=black width=2>�����˺�<color=#FFDD42>+50</color></outline></b>", "type": 3, "time": -1, "isOverlay": 99, "icon": "v1/images/equip/icon_dragonball", "attr": [100], "value": [[50]], "skillId": [9040], "ishideUI": 1}, "102603": {"id": 102603, "name": "���⹥��1", "rarity": 2, "desc": "<b><outline color=black width=2>���⹥��<color=#FFDD42>+10%</color></outline></b>", "type": 3, "time": -1, "isOverlay": 99, "icon": "v1/images/equip/icon_dragonball", "attr": [103, 133], "value": [[-0.1], [0.1]], "skillId": [9040], "ishideUI": 1}, "102604": {"id": 102604, "name": "���⹥��2", "rarity": 3, "desc": "<b><outline color=black width=2>���⹥��<color=#FFDD42>+20%</color></outline></b>", "type": 3, "time": -1, "isOverlay": 99, "icon": "v1/images/equip/icon_dragonball", "attr": [103, 133], "value": [[-0.2], [0.1]], "skillId": [9040], "ishideUI": 1}, "102605": {"id": 102605, "name": "���⹥��3", "rarity": 4, "desc": "<b><outline color=black width=2>���⹥��<color=#FFDD42>+50%</color></outline></b>", "type": 3, "time": -1, "isOverlay": 99, "icon": "v1/images/equip/icon_dragonball", "attr": [103, 133], "value": [[-0.5], [0.1]], "skillId": [9040], "ishideUI": 1}, "102606": {"id": 102606, "name": "����ʱ��1", "rarity": 2, "desc": "<b><outline color=black width=2>����ʱ��<color=#FFDD42>+1</color></outline></b>", "type": 3, "time": -1, "isOverlay": 99, "icon": "v1/images/equip/icon_dragonball", "attr": [104], "value": [[1]], "skillId": [9040], "ishideUI": 1}, "102607": {"id": 102607, "name": "����ʱ��2", "rarity": 3, "desc": "<b><outline color=black width=2>����ʱ��<color=#FFDD42>+2</color></outline></b>", "type": 3, "time": -1, "isOverlay": 99, "icon": "v1/images/equip/icon_dragonball", "attr": [104], "value": [[2]], "skillId": [9040], "ishideUI": 1}, "102608": {"id": 102608, "name": "����ʱ��3", "rarity": 4, "desc": "<b><outline color=black width=2>����ʱ��<color=#FFDD42>+3</color></outline></b>", "type": 3, "time": -1, "isOverlay": 99, "icon": "v1/images/equip/icon_dragonball", "attr": [104], "value": [[3]], "skillId": [9040], "ishideUI": 1}, "102609": {"id": 102609, "name": "���Ⱪ��1", "rarity": 2, "desc": "<b><outline color=black width=2>���Ⱪ��<color=#FFDD42>+10%</color></outline></b>", "type": 3, "time": -1, "isOverlay": 99, "icon": "v1/images/equip/icon_dragonball", "attr": [148], "value": [[0.1]], "skillId": [9040], "ishideUI": 1}, "102610": {"id": 102610, "name": "���Ⱪ��2", "rarity": 3, "desc": "<b><outline color=black width=2>���Ⱪ��<color=#FFDD42>+25%</color></outline></b>", "type": 3, "time": -1, "isOverlay": 99, "icon": "v1/images/equip/icon_dragonball", "attr": [148], "value": [[0.25]], "skillId": [9040], "ishideUI": 1}, "102611": {"id": 102611, "name": "���Ⱪ��3", "rarity": 4, "desc": "<b><outline color=black width=2>���Ⱪ��<color=#FFDD42>+40%</color></outline></b>", "type": 3, "time": -1, "isOverlay": 99, "icon": "v1/images/equip/icon_dragonball", "attr": [148], "value": [[0.4]], "skillId": [9040], "ishideUI": 1}, "102612": {"id": 102612, "name": "������1", "rarity": 2, "desc": "<b><outline color=black width=2>�����˺����<color=#FFDD42>-15%</color></outline></b>", "type": 3, "time": -1, "isOverlay": 99, "icon": "v1/images/equip/icon_dragonball", "attr": [105], "value": [[-0.15]], "skillId": [9040], "ishideUI": 1}, "102613": {"id": 102613, "name": "������2", "rarity": 3, "desc": "<b><outline color=black width=2>�����˺����<color=#FFDD42>-20%</color></outline></b>", "type": 3, "time": -1, "isOverlay": 99, "icon": "v1/images/equip/icon_dragonball", "attr": [105], "value": [[-0.2]], "skillId": [9040], "ishideUI": 1}, "102614": {"id": 102614, "name": "������3", "rarity": 4, "desc": "<b><outline color=black width=2>�����˺����<color=#FFDD42>-30%</color></outline></b>", "type": 3, "time": -1, "isOverlay": 99, "icon": "v1/images/equip/icon_dragonball", "attr": [105], "value": [[-0.3]], "skillId": [9040], "ishideUI": 1}, "102615": {"id": 102615, "name": "���Ⱪ��1", "rarity": 2, "desc": "<b><outline color=black width=2>���Ⱪ��<color=#FFDD42>+25%</color></outline></b>", "type": 3, "time": -1, "isOverlay": 99, "icon": "v1/images/equip/icon_dragonball", "attr": [149], "value": [[0.25]], "skillId": [9040], "ishideUI": 1}, "102616": {"id": 102616, "name": "���Ⱪ��2", "rarity": 3, "desc": "<b><outline color=black width=2>���Ⱪ��<color=#FFDD42>+50%</color></outline></b>", "type": 3, "time": -1, "isOverlay": 99, "icon": "v1/images/equip/icon_dragonball", "attr": [149], "value": [[0.5]], "skillId": [9040], "ishideUI": 1}, "102617": {"id": 102617, "name": "���Ⱪ��3", "rarity": 4, "desc": "<b><outline color=black width=2>���Ⱪ��<color=#FFDD42>+100%</color></outline></b>", "type": 3, "time": -1, "isOverlay": 99, "icon": "v1/images/equip/icon_dragonball", "attr": [149], "value": [[1]], "skillId": [9040], "ishideUI": 1}, "102618": {"id": 102618, "name": "��������", "rarity": 4, "desc": "<b><outline color=black width=2>����<color=#FFDD42>����Χ��</color>�ļ���</outline></b>", "type": 3, "time": -1, "icon": "v1/images/equip/icon_dragonball", "attr": [139], "value": [[66200]], "skillId": [9040], "ishideUI": 1}, "102700": {"id": 102700, "name": "��ǹ�˺�1", "rarity": 2, "desc": "<b><outline color=black width=2>��ǹ�˺�<color=#FFDD42>+50</color></outline></b>", "type": 3, "time": -1, "isOverlay": 99, "icon": "img/ModeBulletsRebound/icon_fd_tltq", "attr": [100], "value": [[50]], "skillId": [9080], "ishideUI": 1}, "102701": {"id": 102701, "name": "��ǹ�˺�2", "rarity": 3, "desc": "<b><outline color=black width=2>��ǹ�˺�<color=#FFDD42>+100</color></outline></b>", "type": 3, "time": -1, "isOverlay": 99, "icon": "img/ModeBulletsRebound/icon_fd_tltq", "attr": [100], "value": [[100]], "skillId": [9080], "ishideUI": 1}, "102702": {"id": 102702, "name": "��ǹ�˺�3", "rarity": 4, "desc": "<b><outline color=black width=2>��ǹ�˺�<color=#FFDD42>+200</color></outline></b>", "type": 3, "time": -1, "isOverlay": 99, "icon": "img/ModeBulletsRebound/icon_fd_tltq", "attr": [100], "value": [[200]], "skillId": [9080], "ishideUI": 1}, "102703": {"id": 102703, "name": "��ǹ����1", "rarity": 2, "desc": "<b><outline color=black width=2>��ǹ����<color=#FFDD42>+10%</color></outline></b>", "type": 3, "time": -1, "isOverlay": 99, "icon": "img/ModeBulletsRebound/icon_fd_tltq", "attr": [103, 133], "value": [[-0.1], [0.1]], "skillId": [9080], "ishideUI": 1}, "102704": {"id": 102704, "name": "��ǹ����2", "rarity": 3, "desc": "<b><outline color=black width=2>��ǹ����<color=#FFDD42>+20%</color></outline></b>", "type": 3, "time": -1, "isOverlay": 99, "icon": "img/ModeBulletsRebound/icon_fd_tltq", "attr": [103, 133], "value": [[-0.2], [0.1]], "skillId": [9080], "ishideUI": 1}, "102705": {"id": 102705, "name": "��ǹ����3", "rarity": 4, "desc": "<b><outline color=black width=2>��ǹ����<color=#FFDD42>+50%</color></outline></b>", "type": 3, "time": -1, "isOverlay": 99, "icon": "img/ModeBulletsRebound/icon_fd_tltq", "attr": [103, 133], "value": [[-0.5], [0.1]], "skillId": [9080], "ishideUI": 1}, "102706": {"id": 102706, "name": "��ǹ����1", "rarity": 2, "desc": "<b><outline color=black width=2>��ǹ����<color=#FFDD42>+1</color></outline></b>", "type": 3, "time": -1, "isOverlay": 99, "icon": "img/ModeBulletsRebound/icon_fd_tltq", "attr": [101], "value": [[1]], "skillId": [9080], "ishideUI": 1}, "102707": {"id": 102707, "name": "��ǹ����2", "rarity": 3, "desc": "<b><outline color=black width=2>��ǹ����<color=#FFDD42>+2</color></outline></b>", "type": 3, "time": -1, "isOverlay": 99, "icon": "img/ModeBulletsRebound/icon_fd_tltq", "attr": [101], "value": [[2]], "skillId": [9080], "ishideUI": 1}, "102708": {"id": 102708, "name": "��ǹ����3", "rarity": 4, "desc": "<b><outline color=black width=2>��ǹ����<color=#FFDD42>+3</color></outline></b>", "type": 3, "time": -1, "isOverlay": 99, "icon": "img/ModeBulletsRebound/icon_fd_tltq", "attr": [101], "value": [[3]], "skillId": [9080], "ishideUI": 1}, "102709": {"id": 102709, "name": "��ǹ��͸1", "rarity": 2, "desc": "<b><outline color=black width=2>��ǹ��͸<color=#FFDD42>+1</color></outline></b>", "type": 3, "time": -1, "isOverlay": 99, "icon": "img/ModeBulletsRebound/icon_fd_tltq", "attr": [120], "value": [[1]], "skillId": [9080], "ishideUI": 1}, "102710": {"id": 102710, "name": "��ǹ��͸2", "rarity": 3, "desc": "<b><outline color=black width=2>��ǹ��͸<color=#FFDD42>+2</color></outline></b>", "type": 3, "time": -1, "isOverlay": 99, "icon": "img/ModeBulletsRebound/icon_fd_tltq", "attr": [120], "value": [[2]], "skillId": [9080], "ishideUI": 1}, "102711": {"id": 102711, "name": "��ǹ��͸3", "rarity": 4, "desc": "<b><outline color=black width=2>��ǹ��͸<color=#FFDD42>+5</color></outline></b>", "type": 3, "time": -1, "isOverlay": 99, "icon": "img/ModeBulletsRebound/icon_fd_tltq", "attr": [120], "value": [[3]], "skillId": [9080], "ishideUI": 1}, "102712": {"id": 102712, "name": "��ǹ����1", "rarity": 2, "desc": "<b><outline color=black width=2>��ǹ����<color=#FFDD42>+10%</color></outline></b>", "type": 3, "time": -1, "isOverlay": 99, "icon": "img/ModeBulletsRebound/icon_fd_tltq", "attr": [148], "value": [[0.1]], "skillId": [9080], "ishideUI": 1}, "102713": {"id": 102713, "name": "��ǹ����2", "rarity": 3, "desc": "<b><outline color=black width=2>��ǹ����<color=#FFDD42>+25%</color></outline></b>", "type": 3, "time": -1, "isOverlay": 99, "icon": "img/ModeBulletsRebound/icon_fd_tltq", "attr": [148], "value": [[0.25]], "skillId": [9080], "ishideUI": 1}, "102714": {"id": 102714, "name": "��ǹ����3", "rarity": 4, "desc": "<b><outline color=black width=2>��ǹ����<color=#FFDD42>+40%</color></outline></b>", "type": 3, "time": -1, "isOverlay": 99, "icon": "img/ModeBulletsRebound/icon_fd_tltq", "attr": [148], "value": [[0.4]], "skillId": [9080], "ishideUI": 1}, "102715": {"id": 102715, "name": "��ǹ����1", "rarity": 2, "desc": "<b><outline color=black width=2>��ǹ����<color=#FFDD42>+25%</color></outline></b>", "type": 3, "time": -1, "isOverlay": 99, "icon": "img/ModeBulletsRebound/icon_fd_tltq", "attr": [149], "value": [[0.25]], "skillId": [9080], "ishideUI": 1}, "102716": {"id": 102716, "name": "��ǹ����2", "rarity": 3, "desc": "<b><outline color=black width=2>��ǹ����<color=#FFDD42>+50%</color></outline></b>", "type": 3, "time": -1, "isOverlay": 99, "icon": "img/ModeBulletsRebound/icon_fd_tltq", "attr": [149], "value": [[0.5]], "skillId": [9080], "ishideUI": 1}, "102717": {"id": 102717, "name": "��ǹ����3", "rarity": 4, "desc": "<b><outline color=black width=2>��ǹ����<color=#FFDD42>+100%</color></outline></b>", "type": 3, "time": -1, "isOverlay": 99, "icon": "img/ModeBulletsRebound/icon_fd_tltq", "attr": [149], "value": [[1]], "skillId": [9080], "ishideUI": 1}, "102718": {"id": 102718, "name": "�����ӵ�", "rarity": 4, "desc": "<b><outline color=black width=2>��ǹ�ӵ��滻Ϊ������,���к��и��ʼ���<color=#FFDD42>2s</color></outline></b>", "type": 3, "time": -1, "icon": "img/ModeBulletsRebound/icon_fd_tltq", "attr": [135, 109], "value": [[41], [190, 5, 3, 0]], "skillId": [9080, 60040]}, "102719": {"id": 102719, "name": "�����ӵ�", "rarity": 4, "desc": "<b><outline color=black width=2>�ӵ����Է���<color=#FFDD42>1��</color></outline></b>", "type": 3, "time": -1, "icon": "img/ModeBulletsRebound/icon_fd_tltq", "attr": [500], "value": [[1]], "skillId": [6000, 60040]}, "102900": {"id": 102900, "name": "�����˺�1", "rarity": 2, "desc": "<b><outline color=black width=2>�����˺�<color=#FFDD42>+30</color></outline></b>", "type": 3, "time": -1, "isOverlay": 99, "icon": "v1/images/equip/icon_basketball", "attr": [100], "value": [[30]], "skillId": [9100], "ishideUI": 1}, "102901": {"id": 102901, "name": "�����˺�2", "rarity": 3, "desc": "<b><outline color=black width=2>�����˺�<color=#FFDD42>+60</color></outline></b>", "type": 3, "time": -1, "isOverlay": 99, "icon": "v1/images/equip/icon_basketball", "attr": [100], "value": [[60]], "skillId": [9100], "ishideUI": 1}, "102902": {"id": 102902, "name": "�����˺�3", "rarity": 4, "desc": "<b><outline color=black width=2>�����˺�<color=#FFDD42>+120</color></outline></b>", "type": 3, "time": -1, "isOverlay": 99, "icon": "v1/images/equip/icon_basketball", "attr": [100], "value": [[120]], "skillId": [9100], "ishideUI": 1}, "102903": {"id": 102903, "name": "������1", "rarity": 2, "desc": "<b><outline color=black width=2>������<color=#FFDD42>+10%</color></outline></b>", "type": 3, "time": -1, "isOverlay": 99, "icon": "v1/images/equip/icon_basketball", "attr": [103, 133], "value": [[-0.1], [0.1]], "skillId": [9100], "ishideUI": 1}, "102904": {"id": 102904, "name": "������2", "rarity": 3, "desc": "<b><outline color=black width=2>������<color=#FFDD42>+20%</color></outline></b>", "type": 3, "time": -1, "isOverlay": 99, "icon": "v1/images/equip/icon_basketball", "attr": [103, 133], "value": [[-0.2], [0.1]], "skillId": [9100], "ishideUI": 1}, "102905": {"id": 102905, "name": "������3", "rarity": 4, "desc": "<b><outline color=black width=2>������<color=#FFDD42>+50%</color></outline></b>", "type": 3, "time": -1, "isOverlay": 99, "icon": "v1/images/equip/icon_basketball", "attr": [103, 133], "value": [[-0.5], [0.1]], "skillId": [9100], "ishideUI": 1}, "102906": {"id": 102906, "name": "���򵯵�1", "rarity": 2, "desc": "<b><outline color=black width=2>���򵯵�<color=#FFDD42>+1</color></outline></b>", "type": 3, "time": -1, "isOverlay": 99, "icon": "v1/images/equip/icon_basketball", "attr": [101], "value": [[1]], "skillId": [9100], "ishideUI": 1}, "102907": {"id": 102907, "name": "���򵯵�2", "rarity": 3, "desc": "<b><outline color=black width=2>���򵯵�<color=#FFDD42>+2</color></outline></b>", "type": 3, "time": -1, "isOverlay": 99, "icon": "v1/images/equip/icon_basketball", "attr": [101], "value": [[2]], "skillId": [9100], "ishideUI": 1}, "102908": {"id": 102908, "name": "���򵯵�3", "rarity": 4, "desc": "<b><outline color=black width=2>���򵯵�<color=#FFDD42>+3</color></outline></b>", "type": 3, "time": -1, "isOverlay": 99, "icon": "v1/images/equip/icon_basketball", "attr": [101], "value": [[3]], "skillId": [9100], "ishideUI": 1}, "102909": {"id": 102909, "name": "����͸1", "rarity": 2, "desc": "<b><outline color=black width=2>������<color=#FFDD42>+1</color></outline></b>", "type": 3, "time": -1, "isOverlay": 99, "icon": "v1/images/equip/icon_basketball", "attr": [500, 120], "value": [[1], [2]], "skillId": [9100], "ishideUI": 1}, "102910": {"id": 102910, "name": "����͸2", "rarity": 3, "desc": "<b><outline color=black width=2>������<color=#FFDD42>+2</color></outline></b>", "type": 3, "time": -1, "isOverlay": 99, "icon": "v1/images/equip/icon_basketball", "attr": [500, 120], "value": [[2], [2]], "skillId": [9100], "ishideUI": 1}, "102911": {"id": 102911, "name": "����͸3", "rarity": 4, "desc": "<b><outline color=black width=2>������<color=#FFDD42>+3</color></outline></b>", "type": 3, "time": -1, "isOverlay": 99, "icon": "v1/images/equip/icon_basketball", "attr": [500, 120], "value": [[3], [2]], "skillId": [9100], "ishideUI": 1}, "102912": {"id": 102912, "name": "���򱩻�1", "rarity": 2, "desc": "<b><outline color=black width=2>���򱩻�<color=#FFDD42>+10%</color></outline></b>", "type": 3, "time": -1, "isOverlay": 99, "icon": "v1/images/equip/icon_basketball", "attr": [148], "value": [[0.1]], "skillId": [9100], "ishideUI": 1}, "102913": {"id": 102913, "name": "���򱩻�2", "rarity": 3, "desc": "<b><outline color=black width=2>���򱩻�<color=#FFDD42>+15%</color></outline></b>", "type": 3, "time": -1, "isOverlay": 99, "icon": "v1/images/equip/icon_basketball", "attr": [148], "value": [[0.15]], "skillId": [9100], "ishideUI": 1}, "102914": {"id": 102914, "name": "���򱩻�3", "rarity": 4, "desc": "<b><outline color=black width=2>���򱩻�<color=#FFDD42>+25%</color></outline></b>", "type": 3, "time": -1, "isOverlay": 99, "icon": "v1/images/equip/icon_basketball", "attr": [148], "value": [[0.25]], "skillId": [9100], "ishideUI": 1}, "102915": {"id": 102915, "name": "������1", "rarity": 2, "desc": "<b><outline color=black width=2>������<color=#FFDD42>+10%</color></outline></b>", "type": 3, "time": -1, "isOverlay": 99, "icon": "v1/images/equip/icon_basketball", "attr": [149], "value": [[0.1]], "skillId": [9100], "ishideUI": 1}, "102916": {"id": 102916, "name": "������2", "rarity": 3, "desc": "<b><outline color=black width=2>������<color=#FFDD42>+25%</color></outline></b>", "type": 3, "time": -1, "isOverlay": 99, "icon": "v1/images/equip/icon_basketball", "attr": [149], "value": [[0.25]], "skillId": [9100], "ishideUI": 1}, "102917": {"id": 102917, "name": "������3", "rarity": 4, "desc": "<b><outline color=black width=2>������<color=#FFDD42>+50%</color></outline></b>", "type": 3, "time": -1, "isOverlay": 99, "icon": "v1/images/equip/icon_basketball", "attr": [149], "value": [[0.5]], "skillId": [9100], "ishideUI": 1}, "103790": {"id": 103790, "name": "ʯ��", "rarity": 2, "desc": "<outline color=black width=2>ʯ��</outline>", "type": 2, "time": 1, "attr": [106], "value": [[0]], "weight": 0.03, "res": ["bones/skill/fx_petrified", "2"], "ishideUI": 1, "object": 1}, "103960": {"id": 103960, "name": "������1��", "rarity": 4, "desc": "<outline color=black width=2>ÿ�ι����и��ʽ���������1��״̬<color=#FFDD42>����������</color>����3s</outline>", "type": 3, "time": 2, "isOverlay": 1, "icon": "v1/images/equip/icon_arrow", "attr": [103], "value": [[-1]], "skillId": [6780, 67800, 67820], "weight": 0.1, "res": ["bones/skill/fx_buff_ad2", "2"], "ishideUI": 1}, "103970": {"id": 103970, "name": "������2��", "rarity": 4, "desc": "<outline color=black width=2>ÿ�ι����и��ʽ���������2����̬,�����ض�<color=#FFDD42>����</color>,��͸��<color=#FFDD42>+2</color>,����3s</outline>", "type": 3, "time": 2, "isOverlay": 1, "icon": "v1/images/equip/icon_arrow", "attr": [148, 120], "value": [[1], [2]], "skillId": [6780, 67800, 67820], "weight": 0.1, "res": ["bones/skill/fx_buff_ad3", "2"], "ishideUI": 1}, "103980": {"id": 103980, "name": "����������", "rarity": 3, "desc": "<outline color=black width=2>�����ӵ�����<color=#FFDD42>25%</color>����2s</outline>", "type": 3, "time": 2, "icon": "v1/images/equip/item_20145", "attr": [8], "value": [[-0.4]], "res": ["bones/skill/fx_deceleration", "2"], "ishideUI": 1}, "103990": {"id": 103990, "name": "���������", "rarity": 2, "desc": "<outline color=black width=2>���ⷢ������ʸ,���е�λ�����<color=#FFDD42>ȼ��</color></outline>", "type": 6, "time": 3, "attr": [124], "value": [[40]], "res": ["bones/skill/fx_burn", "2"], "ishideUI": 1, "object": 1, "className": "B<PERSON>_<PERSON>", "otherValue": [0.5]}, "104000": {"id": 104000, "name": "���", "rarity": 3, "desc": "<outline color=black width=2>�������еĵ�λ�ᱻ���ʹ���ܵ��˺�<color=#FFDD42>+30%</color></outline>", "type": 2, "time": 2, "icon": "v1/images/equip/item_20145", "attr": [127], "value": [[0.3]], "res": ["bones/skill/zhunxing", "2"]}, "104010": {"id": 104010, "name": "�����", "rarity": 3, "desc": "<outline color=black width=2>�������ʱ��25%����,�������<color=#FFDD42>100%</color>�˺�,����2s</outline>", "type": 3, "time": 2, "icon": "v1/images/equip/icon_arrow", "attr": [100], "value": [[1]], "skillId": [6780, 67800, 67820], "weight": 0.1, "res": ["bones/skill/qiliu", "2"], "ishideUI": 1}, "104160": {"id": 104160, "name": "����ǹ��ɱ", "rarity": 3, "desc": "<outline color=black width=2>����ǹ�ӵ���ɱ��λ�ĸ���<color=#FFDD42>+1%</color></outline>", "type": 3, "time": -1, "icon": "v1/images/equip/item_20011", "attr": [115], "value": [[1, 104161]], "skillId": [6800, 68000], "weight": 0, "ishideUI": 1}, "104161": {"id": 104161, "name": "����ǹ��ɱ", "rarity": 3, "desc": "<outline color=black width=2>����ǹ�ӵ���ɱ��λ�ĸ���<color=#FFDD42>+1%</color></outline>", "type": 6, "time": 0.5, "attr": [-1], "value": [[]], "res": ["bones/skill/gth", "2"], "ishideUI": 1, "object": 1, "className": "Buff_SetSlash"}, "104170": {"id": 104170, "name": "�������", "rarity": 3, "desc": "<outline color=black width=2>����ǹ��ϻ��պ��и�������<color=#FFDD42>����</color>��ϻ</outline>", "type": 3, "time": 1, "icon": "v1/images/equip/item_20011", "attr": [103], "value": [[-1]], "skillId": [6800], "weight": 0.15, "res": ["bones/skill/danjia", "2"], "ishideUI": 1}, "104400": {"id": 104400, "name": "����+5", "rarity": 1, "desc": "<b><outline color=black width=2>����<color=#FFDD42>+5</color></outline></b>", "type": 1, "time": -1, "isOverlay": 99, "icon": "img/ModeBulletsRebound/icon_fd_tltq", "attr": [403], "value": [[5]], "ishideUI": 1}, "104401": {"id": 104401, "name": "����+10", "rarity": 1, "desc": "<b><outline color=black width=2>����<color=#FFDD42>+10</color></outline></b>", "type": 1, "time": -1, "isOverlay": 99, "icon": "img/ModeBulletsRebound/icon_fd_tltq", "attr": [403], "value": [[10]], "ishideUI": 1}, "104402": {"id": 104402, "name": "����+20", "rarity": 2, "desc": "<b><outline color=black width=2>����<color=#FFDD42>+20</color></outline></b>", "type": 1, "time": -1, "isOverlay": 99, "icon": "img/ModeBulletsRebound/icon_fd_tltq", "attr": [403], "value": [[20]], "ishideUI": 1}, "104403": {"id": 104403, "name": "����+40", "rarity": 2, "desc": "<b><outline color=black width=2>����<color=#FFDD42>+40</color></outline></b>", "type": 1, "time": -1, "isOverlay": 99, "icon": "img/ModeBulletsRebound/icon_fd_tltq", "attr": [403], "value": [[40]], "ishideUI": 1}, "104404": {"id": 104404, "name": "����+60", "rarity": 2, "desc": "<b><outline color=black width=2>����<color=#FFDD42>+60</color></outline></b>", "type": 1, "time": -1, "isOverlay": 99, "icon": "img/ModeBulletsRebound/icon_fd_tltq", "attr": [403], "value": [[60]], "ishideUI": 1}, "104405": {"id": 104405, "name": "����+100", "rarity": 3, "desc": "<b><outline color=black width=2>����<color=#FFDD42>+100</color></outline></b>", "type": 1, "time": -1, "isOverlay": 99, "icon": "img/ModeBulletsRebound/icon_fd_tltq", "attr": [403], "value": [[100]], "ishideUI": 1}, "104406": {"id": 104406, "name": "����+150", "rarity": 3, "desc": "<b><outline color=black width=2>����<color=#FFDD42>+150</color></outline></b>", "type": 1, "time": -1, "isOverlay": 99, "icon": "img/ModeBulletsRebound/icon_fd_tltq", "attr": [403], "value": [[150]], "ishideUI": 1}, "104407": {"id": 104407, "name": "����+200", "rarity": 3, "desc": "<b><outline color=black width=2>����<color=#FFDD42>+200</color></outline></b>", "type": 1, "time": -1, "isOverlay": 99, "icon": "img/ModeBulletsRebound/icon_fd_tltq", "attr": [403], "value": [[200]], "ishideUI": 1}, "104408": {"id": 104408, "name": "����+500", "rarity": 4, "desc": "<b><outline color=black width=2>����<color=#FFDD42>+500</color></outline></b>", "type": 1, "time": -1, "isOverlay": 99, "icon": "img/ModeBulletsRebound/icon_fd_tltq", "attr": [403], "value": [[500]], "ishideUI": 1}, "104409": {"id": 104409, "name": "����+1000", "rarity": 4, "desc": "<b><outline color=black width=2>����<color=#FFDD42>+1000</color></outline></b>", "type": 1, "time": -1, "isOverlay": 99, "icon": "img/ModeBulletsRebound/icon_fd_tltq", "attr": [403], "value": [[1000]], "ishideUI": 1}, "104410": {"id": 104410, "name": "����x2", "rarity": 2, "desc": "<b><outline color=black width=2>����<color=#FFDD42>x2</color></outline></b>", "type": 1, "time": -1, "isOverlay": 2, "icon": "img/ModeBulletsRebound/icon_fd_tltq", "attr": [3], "value": [[1]], "ishideUI": 1}, "104411": {"id": 104411, "name": "����x3", "rarity": 2, "desc": "<b><outline color=black width=2>����<color=#FFDD42>x3</color></outline></b>", "type": 1, "time": -1, "isOverlay": 2, "icon": "img/ModeBulletsRebound/icon_fd_tltq", "attr": [3], "value": [[2]], "ishideUI": 1}, "104412": {"id": 104412, "name": "����x4", "rarity": 3, "desc": "<b><outline color=black width=2>����<color=#FFDD42>x4</color></outline></b>", "type": 1, "time": -1, "isOverlay": 2, "icon": "img/ModeBulletsRebound/icon_fd_tltq", "attr": [3], "value": [[3]], "ishideUI": 1}, "104413": {"id": 104413, "name": "����x5", "rarity": 3, "desc": "<b><outline color=black width=2>����<color=#FFDD42>x5</color></outline></b>", "type": 1, "time": -1, "isOverlay": 2, "icon": "img/ModeBulletsRebound/icon_fd_tltq", "attr": [3], "value": [[4]], "ishideUI": 1}, "104414": {"id": 104414, "name": "����x10", "rarity": 4, "desc": "<b><outline color=black width=2>����<color=#FFDD42>x10</color></outline></b>", "type": 1, "time": -1, "isOverlay": 2, "icon": "img/ModeBulletsRebound/icon_fd_tltq", "attr": [3], "value": [[10]], "ishideUI": 1}, "104415": {"id": 104415, "name": "����x20", "rarity": 4, "desc": "<b><outline color=black width=2>����<color=#FFDD42>x20</color></outline></b>", "type": 1, "time": -1, "isOverlay": 2, "icon": "img/ModeBulletsRebound/icon_fd_tltq", "attr": [3], "value": [[20]], "ishideUI": 1}, "104416": {"id": 104416, "name": "����x50", "rarity": 4, "desc": "<b><outline color=black width=2>����<color=#FFDD42>x50</color></outline></b>", "type": 1, "time": -1, "isOverlay": 2, "icon": "img/ModeBulletsRebound/icon_fd_tltq", "attr": [3], "value": [[50]], "ishideUI": 1}, "104417": {"id": 104417, "name": "����x100", "rarity": 4, "desc": "<b><outline color=black width=2>����<color=#FFDD42>x1000</color></outline></b>", "type": 1, "time": -1, "isOverlay": 2, "icon": "img/ModeBulletsRebound/icon_fd_tltq", "attr": [3], "value": [[100]], "ishideUI": 1}, "104420": {"id": 104420, "name": "����+1", "rarity": 2, "desc": "<b><outline color=black width=2>����<color=#FFDD42>+1</color></outline></b>", "type": 3, "time": -1, "isOverlay": 99, "icon": "img/ModeBulletsRebound/icon_fd_tltq", "attr": [103, 133], "value": [[-0.1], [0.05]], "skillId": [9120, 91200, 91220, 91240, 91260, 9140, 9160, 91600, 91620, 91640, 91660], "ishideUI": 1}, "104421": {"id": 104421, "name": "����x2", "rarity": 3, "desc": "<b><outline color=black width=2>����<color=#FFDD42>x2</color></outline></b>", "type": 3, "time": -1, "isOverlay": 99, "icon": "img/ModeBulletsRebound/icon_fd_tltq", "attr": [103, 133], "value": [[-0.2], [0.05]], "skillId": [9120, 91200, 91220, 91240, 91260, 9140, 9160, 91600, 91620, 91640, 91660], "ishideUI": 1}, "104422": {"id": 104422, "name": "����x6", "rarity": 4, "desc": "<b><outline color=black width=2>����<color=#FFDD42>x6</color></outline></b>", "type": 3, "time": -1, "isOverlay": 99, "icon": "img/ModeBulletsRebound/icon_fd_tltq", "attr": [103, 133], "value": [[-0.6], [0.1]], "skillId": [9120, 91200, 91220, 91240, 91260, 9140, 9160, 91600, 91620, 91640, 91660], "ishideUI": 1}, "104430": {"id": 104430, "name": "����1", "rarity": 2, "desc": "<b><outline color=black width=2>����<color=#FFDD42>+1</color></outline></b>", "type": 3, "time": -1, "isOverlay": 99, "icon": "img/ModeBulletsRebound/icon_fd_tltq", "attr": [101], "value": [[1]], "skillId": [9120, 91200, 91220, 91240, 91260, 9140, 9160, 91600, 91620, 91640, 91660], "ishideUI": 1}, "104431": {"id": 104431, "name": "����2", "rarity": 3, "desc": "<b><outline color=black width=2>����<color=#FFDD42>+2</color></outline></b>", "type": 3, "time": -1, "isOverlay": 99, "icon": "img/ModeBulletsRebound/icon_fd_tltq", "attr": [101], "value": [[2]], "skillId": [9120, 91200, 91220, 91240, 91260, 9140, 9160, 91600, 91620, 91640, 91660], "ishideUI": 1}, "104432": {"id": 104432, "name": "����3", "rarity": 4, "desc": "<b><outline color=black width=2>����<color=#FFDD42>+3</color></outline></b>", "type": 3, "time": -1, "isOverlay": 99, "icon": "img/ModeBulletsRebound/icon_fd_tltq", "attr": [101], "value": [[3]], "skillId": [9120, 91200, 91220, 91240, 91260, 9140, 9160, 91600, 91620, 91640, 91660], "ishideUI": 1}, "104440": {"id": 104440, "name": "����+1", "rarity": 3, "desc": "<b><outline color=black width=2>����<color=#FFDD42>+1</color></outline></b>", "type": 4, "time": 0, "isOverlay": 1, "icon": "img/ModeBulletsRebound/icon_fd_tltq", "attr": [143], "value": [[91200]], "ishideUI": 1}, "104441": {"id": 104441, "name": "����+1", "rarity": 3, "desc": "<b><outline color=black width=2>����<color=#FFDD42>+1</color></outline></b>", "type": 4, "time": 0, "isOverlay": 1, "icon": "img/ModeBulletsRebound/icon_fd_tltq", "attr": [143], "value": [[91220]], "ishideUI": 1}, "104442": {"id": 104442, "name": "����+1", "rarity": 3, "desc": "<b><outline color=black width=2>����<color=#FFDD42>+1</color></outline></b>", "type": 4, "time": 0, "isOverlay": 1, "icon": "img/ModeBulletsRebound/icon_fd_tltq", "attr": [143], "value": [[91240]], "ishideUI": 1}, "104443": {"id": 104443, "name": "����+1", "rarity": 3, "desc": "<b><outline color=black width=2>����<color=#FFDD42>+1</color></outline></b>", "type": 4, "time": 0, "isOverlay": 1, "icon": "img/ModeBulletsRebound/icon_fd_tltq", "attr": [143], "value": [[91260]], "ishideUI": 1}, "104450": {"id": 104450, "name": "����", "rarity": 4, "desc": "<b><outline color=black width=2>���<color=#FFDD42>����</color></outline></b>", "type": 3, "time": -1, "isOverlay": 1, "icon": "v1/images/equip/icon_dragonball", "attr": [139], "value": [[9140]], "skillId": [9120], "ishideUI": 1}, "104451": {"id": 104451, "name": "���ǹ", "rarity": 4, "desc": "<b><outline color=black width=2>���<color=#FFDD42>���ǹ</color></outline></b>", "type": 4, "time": -1, "isOverlay": 1, "icon": "img/ModeBulletsRebound/icon_fd_tltq", "attr": [139], "value": [[9160]], "skillId": [9120], "ishideUI": 1}, "104452": {"id": 104452, "name": "����+1", "rarity": 3, "desc": "<b><outline color=black width=2>����<color=#FFDD42>+1</color></outline></b>", "type": 4, "time": 0, "isOverlay": 1, "icon": "img/ModeBulletsRebound/icon_fd_tltq", "attr": [143], "value": [[91600]], "ishideUI": 1}, "104453": {"id": 104453, "name": "����+1", "rarity": 3, "desc": "<b><outline color=black width=2>����<color=#FFDD42>+1</color></outline></b>", "type": 4, "time": 0, "isOverlay": 1, "icon": "img/ModeBulletsRebound/icon_fd_tltq", "attr": [143], "value": [[91620]], "ishideUI": 1}, "104454": {"id": 104454, "name": "����+1", "rarity": 3, "desc": "<b><outline color=black width=2>����<color=#FFDD42>+1</color></outline></b>", "type": 4, "time": 0, "isOverlay": 1, "icon": "img/ModeBulletsRebound/icon_fd_tltq", "attr": [143], "value": [[91640]], "ishideUI": 1}, "104455": {"id": 104455, "name": "����+1", "rarity": 3, "desc": "<b><outline color=black width=2>����<color=#FFDD42>+1</color></outline></b>", "type": 4, "time": 0, "isOverlay": 1, "icon": "img/ModeBulletsRebound/icon_fd_tltq", "attr": [143], "value": [[91660]], "ishideUI": 1}, "104460": {"id": 104460, "name": "�����˺�", "rarity": 2, "desc": "<b><outline color=black width=2>�����˺�<color=#FFDD42>+10%</color></outline></b>", "type": 3, "time": -1, "isOverlay": 99, "icon": "img/ModeBulletsRebound/icon_fd_tltq", "attr": [149], "value": [[0.1]], "skillId": [9160, 91600, 91620, 91640, 91660], "ishideUI": 1}, "104461": {"id": 104461, "name": "�����˺�", "rarity": 3, "desc": "<b><outline color=black width=2>�����˺�<color=#FFDD42>+40%</color></outline></b>", "type": 3, "time": -1, "isOverlay": 99, "icon": "img/ModeBulletsRebound/icon_fd_tltq", "attr": [149], "value": [[0.4]], "skillId": [9160, 91600, 91620, 91640, 91660], "ishideUI": 1}, "104462": {"id": 104462, "name": "�����˺�", "rarity": 4, "desc": "<b><outline color=black width=2>�����˺�<color=#FFDD42>+100%</color></outline></b>", "type": 3, "time": -1, "isOverlay": 99, "icon": "img/ModeBulletsRebound/icon_fd_tltq", "attr": [149], "value": [[1]], "skillId": [9160, 91600, 91620, 91640, 91660], "ishideUI": 1}, "104470": {"id": 104470, "name": "������", "rarity": 2, "desc": "<b><outline color=black width=2>������<color=#FFDD42>+5%</color></outline></b>", "type": 3, "time": -1, "isOverlay": 99, "icon": "img/ModeBulletsRebound/icon_fd_tltq", "attr": [148], "value": [[0.05]], "skillId": [9160, 91600, 91620, 91640, 91660], "ishideUI": 1}, "104471": {"id": 104471, "name": "������", "rarity": 3, "desc": "<b><outline color=black width=2>������<color=#FFDD42>+10%</color></outline></b>", "type": 3, "time": -1, "isOverlay": 99, "icon": "img/ModeBulletsRebound/icon_fd_tltq", "attr": [148], "value": [[0.1]], "skillId": [9160, 91600, 91620, 91640, 91660], "ishideUI": 1}, "104472": {"id": 104472, "name": "������", "rarity": 4, "desc": "<b><outline color=black width=2>������<color=#FFDD42>+20%</color></outline></b>", "type": 3, "time": -1, "isOverlay": 99, "icon": "img/ModeBulletsRebound/icon_fd_tltq", "attr": [148], "value": [[0.2]], "skillId": [9160, 91600, 91620, 91640, 91660], "ishideUI": 1}, "104480": {"id": 104480, "name": "��͸", "rarity": 3, "desc": "<b><outline color=black width=2>��͸<color=#FFDD42>+1</color></outline></b>", "type": 3, "time": -1, "isOverlay": 99, "icon": "img/ModeBulletsRebound/icon_fd_tltq", "attr": [120], "value": [[1]], "skillId": [9160, 91600, 91620, 91640, 91660], "ishideUI": 1}, "104481": {"id": 104481, "name": "��͸", "rarity": 4, "desc": "<b><outline color=black width=2>��͸<color=#FFDD42>+2</color></outline></b>", "type": 3, "time": -1, "isOverlay": 99, "icon": "img/ModeBulletsRebound/icon_fd_tltq", "attr": [120], "value": [[2]], "skillId": [9160, 91600, 91620, 91640, 91660], "ishideUI": 1}, "142000": {"id": 142000, "name": "�𹿰����", "rarity": 4, "desc": "<outline color=black width=2>�𹿰���ת�ڼ�������</outline>", "type": 1, "time": -1, "isOverlay": 7, "icon": "v1/images/equip/icon_Spear", "attr": [13], "value": [[0.1]], "ishideUI": 1, "className": "Buff_OnTime", "otherValue": [0.1]}, "142120": {"id": 142120, "name": "��������", "rarity": 4, "desc": "<outline color=black width=2>������������ת�ڼ���ͷ�<color=#FFDD42>����</color>���ض�<color=#FFDD42>����</color></outline>", "type": 1, "time": -1, "icon": "v1/images/equip/item_20173", "attr": [5], "value": [[1]], "skillId": [62600], "ishideUI": 1}, "144000": {"id": 144000, "name": "����", "rarity": 2, "desc": "<outline color=black width=2>����ֵ����ٶȳ���<color=#FFDD42>����</color></outline>", "type": 1, "time": -1, "isOverlay": 10, "icon": "v1/images/equip/item_20025", "attr": [8], "value": [[-0.15]], "ishideUI": 1, "className": "Buff_OnTime", "otherValue": [0.5]}, "602115": {"id": 602115, "name": "����", "rarity": 4, "desc": "<outline color=black width=2>������ͷź�����<color=#FFDD42>���</color></outline>", "type": 1, "time": -1, "isOverlay": 25, "icon": "v1/images/equip/item_20025", "attr": [13], "value": [[0.05]], "ishideUI": 1, "className": "Buff_OnTime", "otherValue": [0.1]}, "1044300": {"id": 1044300, "name": "��������", "rarity": 2, "desc": "<outline color=black width=2>�������п�<color=#FFDD42>��ȼ</color>��λ,���<color=#FFDD42>��Ƶ</color>�˺�,����3s</outline>", "type": 6, "time": 3, "attr": [124], "value": [[10]], "res": ["bones/skill/fx_burn", "2"], "ishideUI": 1, "object": 1, "className": "B<PERSON>_<PERSON>", "otherValue": [0.1]}}