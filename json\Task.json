{"1": {"id": 1, "desc": "鐧诲綍娓告垙", "beyongd": 1, "type": 1, "value": [1], "rewards": [[23, 10]]}, "2": {"id": 2, "desc": "姣忔棩鍟嗗簵璐拱1娆�", "beyongd": 1, "type": 3, "value": [1], "rewards": [[23, 10]]}, "3": {"id": 3, "desc": "鎵崱1娆�", "beyongd": 1, "type": 2, "value": [1], "rewards": [[23, 10]]}, "4": {"id": 4, "desc": "娑堣€楅捇鐭�100", "beyongd": 1, "type": 5, "value": [100], "rewards": [[23, 20]]}, "5": {"id": 5, "desc": "娑堣€楅噾甯�500", "beyongd": 1, "type": 6, "value": [500], "rewards": [[23, 10]]}, "6": {"id": 6, "desc": "娑堣€椾綋鍔�30", "beyongd": 1, "type": 7, "value": [30], "rewards": [[23, 20]]}, "7": {"id": 7, "desc": "鐪嬭棰�1娆�", "beyongd": 1, "type": 8, "value": [1], "rewards": [[23, 20]]}, "8": {"id": 8, "desc": "绱鐧诲綍5澶�", "beyongd": 2, "type": 1, "value": [5], "rewards": [[24, 20]]}, "9": {"id": 9, "desc": "姣忔棩鍟嗗簵璐拱10娆�", "beyongd": 2, "type": 3, "value": [10], "rewards": [[24, 10]]}, "10": {"id": 10, "desc": "娑堣€椾綋鍔�100", "beyongd": 2, "type": 7, "value": [100], "rewards": [[24, 20]]}, "11": {"id": 11, "desc": "娑堣€楅捇鐭�1000", "beyongd": 2, "type": 5, "value": [1000], "rewards": [[24, 20]]}, "12": {"id": 12, "desc": "鎵崱10娆�", "beyongd": 2, "type": 2, "value": [10], "rewards": [[24, 10]]}, "13": {"id": 13, "desc": "瀹濈寮€鍚�10娆�", "beyongd": 2, "type": 11, "value": [10], "rewards": [[24, 20]]}, "1001": {"id": 1001, "desc": "绱鐧诲綍1澶�", "beyongd": 3, "type": 1, "value": [1], "rewards": [[11, 10]]}, "1002": {"id": 1002, "desc": "绱鐧诲綍2澶�", "beyongd": 3, "type": 1, "value": [2], "rewards": [[11, 10]], "pre": 1001}, "1003": {"id": 1003, "desc": "绱鐧诲綍3澶�", "beyongd": 3, "type": 1, "value": [3], "rewards": [[11, 10]], "pre": 1002}, "1004": {"id": 1004, "desc": "绱鐧诲綍4澶�", "beyongd": 3, "type": 1, "value": [4], "rewards": [[11, 10]], "pre": 1003}, "1005": {"id": 1005, "desc": "绱鐧诲綍5澶�", "beyongd": 3, "type": 1, "value": [5], "rewards": [[11, 10]], "pre": 1004}, "1006": {"id": 1006, "desc": "绱鐧诲綍6澶�", "beyongd": 3, "type": 1, "value": [6], "rewards": [[11, 10]], "pre": 1005}, "1007": {"id": 1007, "desc": "绱鐧诲綍7澶�", "beyongd": 3, "type": 1, "value": [7], "rewards": [[11, 10]], "pre": 1006}, "1008": {"id": 1008, "desc": "绱鐧诲綍8澶�", "beyongd": 3, "type": 1, "value": [8], "rewards": [[11, 10]], "pre": 1007}, "1009": {"id": 1009, "desc": "绱鐧诲綍9澶�", "beyongd": 3, "type": 1, "value": [9], "rewards": [[11, 10]], "pre": 1008}, "1010": {"id": 1010, "desc": "绱鐧诲綍10澶�", "beyongd": 3, "type": 1, "value": [10], "rewards": [[11, 10]], "pre": 1009}, "1011": {"id": 1011, "desc": "绱鐧诲綍11澶�", "beyongd": 3, "type": 1, "value": [11], "rewards": [[11, 10]], "pre": 1010}, "1012": {"id": 1012, "desc": "绱鐧诲綍12澶�", "beyongd": 3, "type": 1, "value": [12], "rewards": [[11, 10]], "pre": 1011}, "1013": {"id": 1013, "desc": "绱鐧诲綍13澶�", "beyongd": 3, "type": 1, "value": [13], "rewards": [[11, 10]], "pre": 1012}, "1014": {"id": 1014, "desc": "绱鐧诲綍14澶�", "beyongd": 3, "type": 1, "value": [14], "rewards": [[11, 10]], "pre": 1013}, "1015": {"id": 1015, "desc": "绱鐧诲綍15澶�", "beyongd": 3, "type": 1, "value": [15], "rewards": [[11, 10]], "pre": 1014}, "1016": {"id": 1016, "desc": "绱鐧诲綍16澶�", "beyongd": 3, "type": 1, "value": [16], "rewards": [[11, 10]], "pre": 1015}, "1017": {"id": 1017, "desc": "绱鐧诲綍17澶�", "beyongd": 3, "type": 1, "value": [17], "rewards": [[11, 10]], "pre": 1016}, "1018": {"id": 1018, "desc": "绱鐧诲綍18澶�", "beyongd": 3, "type": 1, "value": [18], "rewards": [[11, 10]], "pre": 1017}, "1019": {"id": 1019, "desc": "绱鐧诲綍19澶�", "beyongd": 3, "type": 1, "value": [19], "rewards": [[11, 10]], "pre": 1018}, "1020": {"id": 1020, "desc": "绱鐧诲綍20澶�", "beyongd": 3, "type": 1, "value": [20], "rewards": [[11, 10]], "pre": 1019}, "1021": {"id": 1021, "desc": "绱鐧诲綍21澶�", "beyongd": 3, "type": 1, "value": [21], "rewards": [[11, 10]], "pre": 1020}, "1022": {"id": 1022, "desc": "绱鐧诲綍22澶�", "beyongd": 3, "type": 1, "value": [22], "rewards": [[11, 10]], "pre": 1021}, "1023": {"id": 1023, "desc": "绱鐧诲綍23澶�", "beyongd": 3, "type": 1, "value": [23], "rewards": [[11, 10]], "pre": 1022}, "1024": {"id": 1024, "desc": "绱鐧诲綍24澶�", "beyongd": 3, "type": 1, "value": [24], "rewards": [[11, 10]], "pre": 1023}, "1025": {"id": 1025, "desc": "绱鐧诲綍25澶�", "beyongd": 3, "type": 1, "value": [25], "rewards": [[11, 10]], "pre": 1024}, "1026": {"id": 1026, "desc": "绱鐧诲綍26澶�", "beyongd": 3, "type": 1, "value": [26], "rewards": [[11, 10]], "pre": 1025}, "1027": {"id": 1027, "desc": "绱鐧诲綍27澶�", "beyongd": 3, "type": 1, "value": [27], "rewards": [[11, 10]], "pre": 1026}, "1028": {"id": 1028, "desc": "绱鐧诲綍28澶�", "beyongd": 3, "type": 1, "value": [28], "rewards": [[11, 10]], "pre": 1027}, "1029": {"id": 1029, "desc": "绱鐧诲綍29澶�", "beyongd": 3, "type": 1, "value": [29], "rewards": [[11, 10]], "pre": 1028}, "1030": {"id": 1030, "desc": "绱鐧诲綍30澶�", "beyongd": 3, "type": 1, "value": [30], "rewards": [[11, 10]], "pre": 1029}, "2000": {"id": 2000, "desc": "閫氬叧涓荤嚎绗�1鍏�", "beyongd": 3, "type": 10, "value": [1], "rewards": [[11, 10]]}, "2001": {"id": 2001, "desc": "閫氬叧涓荤嚎绗�2鍏�", "beyongd": 3, "type": 10, "value": [2], "rewards": [[11, 10]], "pre": 2000}, "2002": {"id": 2002, "desc": "閫氬叧涓荤嚎绗�3鍏�", "beyongd": 3, "type": 10, "value": [3], "rewards": [[11, 10]], "pre": 2001}, "2003": {"id": 2003, "desc": "閫氬叧涓荤嚎绗�4鍏�", "beyongd": 3, "type": 10, "value": [4], "rewards": [[11, 10]], "pre": 2002}, "2004": {"id": 2004, "desc": "閫氬叧涓荤嚎绗�5鍏�", "beyongd": 3, "type": 10, "value": [5], "rewards": [[11, 10]], "pre": 2003}, "2005": {"id": 2005, "desc": "閫氬叧涓荤嚎绗�6鍏�", "beyongd": 3, "type": 10, "value": [6], "rewards": [[11, 10]], "pre": 2004}, "2006": {"id": 2006, "desc": "閫氬叧涓荤嚎绗�7鍏�", "beyongd": 3, "type": 10, "value": [7], "rewards": [[11, 10]], "pre": 2005}, "2007": {"id": 2007, "desc": "閫氬叧涓荤嚎绗�8鍏�", "beyongd": 3, "type": 10, "value": [8], "rewards": [[11, 10]], "pre": 2006}, "2008": {"id": 2008, "desc": "閫氬叧涓荤嚎绗�9鍏�", "beyongd": 3, "type": 10, "value": [9], "rewards": [[11, 10]], "pre": 2007}, "2009": {"id": 2009, "desc": "閫氬叧涓荤嚎绗�10鍏�", "beyongd": 3, "type": 10, "value": [10], "rewards": [[11, 10]], "pre": 2008}, "2010": {"id": 2010, "desc": "閫氬叧涓荤嚎绗�11鍏�", "beyongd": 3, "type": 10, "value": [11], "rewards": [[11, 10]], "pre": 2009}, "2011": {"id": 2011, "desc": "閫氬叧涓荤嚎绗�12鍏�", "beyongd": 3, "type": 10, "value": [12], "rewards": [[11, 10]], "pre": 2010}, "2012": {"id": 2012, "desc": "閫氬叧涓荤嚎绗�13鍏�", "beyongd": 3, "type": 10, "value": [13], "rewards": [[11, 10]], "pre": 2011}, "2013": {"id": 2013, "desc": "閫氬叧涓荤嚎绗�14鍏�", "beyongd": 3, "type": 10, "value": [14], "rewards": [[11, 10]], "pre": 2012}, "2014": {"id": 2014, "desc": "閫氬叧涓荤嚎绗�15鍏�", "beyongd": 3, "type": 10, "value": [15], "rewards": [[11, 10]], "pre": 2013}, "2015": {"id": 2015, "desc": "閫氬叧涓荤嚎绗�16鍏�", "beyongd": 3, "type": 10, "value": [16], "rewards": [[11, 10]], "pre": 2014}, "2016": {"id": 2016, "desc": "閫氬叧涓荤嚎绗�17鍏�", "beyongd": 3, "type": 10, "value": [17], "rewards": [[11, 10]], "pre": 2015}, "2017": {"id": 2017, "desc": "閫氬叧涓荤嚎绗�18鍏�", "beyongd": 3, "type": 10, "value": [18], "rewards": [[11, 10]], "pre": 2016}, "2018": {"id": 2018, "desc": "閫氬叧涓荤嚎绗�19鍏�", "beyongd": 3, "type": 10, "value": [19], "rewards": [[11, 10]], "pre": 2017}, "2019": {"id": 2019, "desc": "閫氬叧涓荤嚎绗�20鍏�", "beyongd": 3, "type": 10, "value": [20], "rewards": [[11, 10]], "pre": 2018}, "2020": {"id": 2020, "desc": "閫氬叧涓荤嚎绗�21鍏�", "beyongd": 3, "type": 10, "value": [21], "rewards": [[11, 10]], "pre": 2019}, "2021": {"id": 2021, "desc": "閫氬叧涓荤嚎绗�22鍏�", "beyongd": 3, "type": 10, "value": [22], "rewards": [[11, 10]], "pre": 2020}, "2022": {"id": 2022, "desc": "閫氬叧涓荤嚎绗�23鍏�", "beyongd": 3, "type": 10, "value": [23], "rewards": [[11, 10]], "pre": 2021}, "2023": {"id": 2023, "desc": "閫氬叧涓荤嚎绗�24鍏�", "beyongd": 3, "type": 10, "value": [24], "rewards": [[11, 10]], "pre": 2022}, "2024": {"id": 2024, "desc": "閫氬叧涓荤嚎绗�25鍏�", "beyongd": 3, "type": 10, "value": [25], "rewards": [[11, 10]], "pre": 2023}, "2025": {"id": 2025, "desc": "閫氬叧涓荤嚎绗�26鍏�", "beyongd": 3, "type": 10, "value": [26], "rewards": [[11, 10]], "pre": 2024}, "2026": {"id": 2026, "desc": "閫氬叧涓荤嚎绗�27鍏�", "beyongd": 3, "type": 10, "value": [27], "rewards": [[11, 10]], "pre": 2025}, "2027": {"id": 2027, "desc": "閫氬叧涓荤嚎绗�28鍏�", "beyongd": 3, "type": 10, "value": [28], "rewards": [[11, 10]], "pre": 2026}, "2028": {"id": 2028, "desc": "閫氬叧涓荤嚎绗�29鍏�", "beyongd": 3, "type": 10, "value": [29], "rewards": [[11, 10]], "pre": 2027}, "2029": {"id": 2029, "desc": "閫氬叧涓荤嚎绗�30鍏�", "beyongd": 3, "type": 10, "value": [30], "rewards": [[11, 10]], "pre": 2028}, "2030": {"id": 2030, "desc": "閫氬叧涓荤嚎绗�31鍏�", "beyongd": 3, "type": 10, "value": [31], "rewards": [[11, 10]], "pre": 2029}, "2031": {"id": 2031, "desc": "閫氬叧涓荤嚎绗�32鍏�", "beyongd": 3, "type": 10, "value": [32], "rewards": [[11, 10]], "pre": 2030}, "2032": {"id": 2032, "desc": "閫氬叧涓荤嚎绗�33鍏�", "beyongd": 3, "type": 10, "value": [33], "rewards": [[11, 10]], "pre": 2031}, "2033": {"id": 2033, "desc": "閫氬叧涓荤嚎绗�34鍏�", "beyongd": 3, "type": 10, "value": [34], "rewards": [[11, 10]], "pre": 2032}, "2034": {"id": 2034, "desc": "閫氬叧涓荤嚎绗�35鍏�", "beyongd": 3, "type": 10, "value": [35], "rewards": [[11, 10]], "pre": 2033}, "2035": {"id": 2035, "desc": "閫氬叧涓荤嚎绗�36鍏�", "beyongd": 3, "type": 10, "value": [36], "rewards": [[11, 10]], "pre": 2034}, "2036": {"id": 2036, "desc": "閫氬叧涓荤嚎绗�37鍏�", "beyongd": 3, "type": 10, "value": [37], "rewards": [[11, 10]], "pre": 2035}, "2037": {"id": 2037, "desc": "閫氬叧涓荤嚎绗�38鍏�", "beyongd": 3, "type": 10, "value": [38], "rewards": [[11, 10]], "pre": 2036}, "2038": {"id": 2038, "desc": "閫氬叧涓荤嚎绗�39鍏�", "beyongd": 3, "type": 10, "value": [39], "rewards": [[11, 10]], "pre": 2037}, "2039": {"id": 2039, "desc": "閫氬叧涓荤嚎绗�40鍏�", "beyongd": 3, "type": 10, "value": [40], "rewards": [[11, 10]], "pre": 2038}, "2040": {"id": 2040, "desc": "閫氬叧涓荤嚎绗�41鍏�", "beyongd": 3, "type": 10, "value": [41], "rewards": [[11, 10]], "pre": 2039}, "2041": {"id": 2041, "desc": "閫氬叧涓荤嚎绗�42鍏�", "beyongd": 3, "type": 10, "value": [42], "rewards": [[11, 10]], "pre": 2040}, "2042": {"id": 2042, "desc": "閫氬叧涓荤嚎绗�43鍏�", "beyongd": 3, "type": 10, "value": [43], "rewards": [[11, 10]], "pre": 2041}, "2043": {"id": 2043, "desc": "閫氬叧涓荤嚎绗�44鍏�", "beyongd": 3, "type": 10, "value": [44], "rewards": [[11, 10]], "pre": 2042}, "2044": {"id": 2044, "desc": "閫氬叧涓荤嚎绗�45鍏�", "beyongd": 3, "type": 10, "value": [45], "rewards": [[11, 10]], "pre": 2043}, "2045": {"id": 2045, "desc": "閫氬叧涓荤嚎绗�46鍏�", "beyongd": 3, "type": 10, "value": [46], "rewards": [[11, 10]], "pre": 2044}, "2046": {"id": 2046, "desc": "閫氬叧涓荤嚎绗�47鍏�", "beyongd": 3, "type": 10, "value": [47], "rewards": [[11, 10]], "pre": 2045}, "2047": {"id": 2047, "desc": "閫氬叧涓荤嚎绗�48鍏�", "beyongd": 3, "type": 10, "value": [48], "rewards": [[11, 10]], "pre": 2046}, "2048": {"id": 2048, "desc": "閫氬叧涓荤嚎绗�49鍏�", "beyongd": 3, "type": 10, "value": [49], "rewards": [[11, 10]], "pre": 2047}, "2049": {"id": 2049, "desc": "閫氬叧涓荤嚎绗�50鍏�", "beyongd": 3, "type": 10, "value": [50], "rewards": [[11, 10]], "pre": 2048}, "2050": {"id": 2050, "desc": "閫氬叧涓荤嚎绗�51鍏�", "beyongd": 3, "type": 10, "value": [51], "rewards": [[11, 10]], "pre": 2049}, "2051": {"id": 2051, "desc": "閫氬叧涓荤嚎绗�52鍏�", "beyongd": 3, "type": 10, "value": [52], "rewards": [[11, 10]], "pre": 2050}, "2052": {"id": 2052, "desc": "閫氬叧涓荤嚎绗�53鍏�", "beyongd": 3, "type": 10, "value": [53], "rewards": [[11, 10]], "pre": 2051}, "2053": {"id": 2053, "desc": "閫氬叧涓荤嚎绗�54鍏�", "beyongd": 3, "type": 10, "value": [54], "rewards": [[11, 10]], "pre": 2052}, "2054": {"id": 2054, "desc": "閫氬叧涓荤嚎绗�55鍏�", "beyongd": 3, "type": 10, "value": [55], "rewards": [[11, 10]], "pre": 2053}, "2055": {"id": 2055, "desc": "閫氬叧涓荤嚎绗�56鍏�", "beyongd": 3, "type": 10, "value": [56], "rewards": [[11, 10]], "pre": 2054}, "2056": {"id": 2056, "desc": "閫氬叧涓荤嚎绗�57鍏�", "beyongd": 3, "type": 10, "value": [57], "rewards": [[11, 10]], "pre": 2055}, "2057": {"id": 2057, "desc": "閫氬叧涓荤嚎绗�58鍏�", "beyongd": 3, "type": 10, "value": [58], "rewards": [[11, 10]], "pre": 2056}, "2058": {"id": 2058, "desc": "閫氬叧涓荤嚎绗�59鍏�", "beyongd": 3, "type": 10, "value": [59], "rewards": [[11, 10]], "pre": 2057}, "2059": {"id": 2059, "desc": "閫氬叧涓荤嚎绗�60鍏�", "beyongd": 3, "type": 10, "value": [60], "rewards": [[11, 10]], "pre": 2058}, "2060": {"id": 2060, "desc": "閫氬叧涓荤嚎绗�61鍏�", "beyongd": 3, "type": 10, "value": [61], "rewards": [[11, 10]], "pre": 2059}, "2061": {"id": 2061, "desc": "閫氬叧涓荤嚎绗�62鍏�", "beyongd": 3, "type": 10, "value": [62], "rewards": [[11, 10]], "pre": 2060}, "2062": {"id": 2062, "desc": "閫氬叧涓荤嚎绗�63鍏�", "beyongd": 3, "type": 10, "value": [63], "rewards": [[11, 10]], "pre": 2061}, "2063": {"id": 2063, "desc": "閫氬叧涓荤嚎绗�64鍏�", "beyongd": 3, "type": 10, "value": [64], "rewards": [[11, 10]], "pre": 2062}, "2064": {"id": 2064, "desc": "閫氬叧涓荤嚎绗�65鍏�", "beyongd": 3, "type": 10, "value": [65], "rewards": [[11, 10]], "pre": 2063}, "2065": {"id": 2065, "desc": "閫氬叧涓荤嚎绗�66鍏�", "beyongd": 3, "type": 10, "value": [66], "rewards": [[11, 10]], "pre": 2064}, "2066": {"id": 2066, "desc": "閫氬叧涓荤嚎绗�67鍏�", "beyongd": 3, "type": 10, "value": [67], "rewards": [[11, 10]], "pre": 2065}, "2067": {"id": 2067, "desc": "閫氬叧涓荤嚎绗�68鍏�", "beyongd": 3, "type": 10, "value": [68], "rewards": [[11, 10]], "pre": 2066}, "2068": {"id": 2068, "desc": "閫氬叧涓荤嚎绗�69鍏�", "beyongd": 3, "type": 10, "value": [69], "rewards": [[11, 10]], "pre": 2067}, "2069": {"id": 2069, "desc": "閫氬叧涓荤嚎绗�70鍏�", "beyongd": 3, "type": 10, "value": [70], "rewards": [[11, 10]], "pre": 2068}, "3000": {"id": 3000, "desc": "鎵崱5娆�", "beyongd": 3, "type": 2, "value": [5], "rewards": [[11, 10]]}, "3001": {"id": 3001, "desc": "鎵崱10娆�", "beyongd": 3, "type": 2, "value": [10], "rewards": [[11, 10]], "pre": 3000}, "3002": {"id": 3002, "desc": "鎵崱15娆�", "beyongd": 3, "type": 2, "value": [15], "rewards": [[11, 10]], "pre": 3001}, "3003": {"id": 3003, "desc": "鎵崱20娆�", "beyongd": 3, "type": 2, "value": [20], "rewards": [[11, 10]], "pre": 3002}, "3004": {"id": 3004, "desc": "鎵崱25娆�", "beyongd": 3, "type": 2, "value": [25], "rewards": [[11, 10]], "pre": 3003}, "3005": {"id": 3005, "desc": "鎵崱30娆�", "beyongd": 3, "type": 2, "value": [30], "rewards": [[11, 10]], "pre": 3004}, "3006": {"id": 3006, "desc": "鎵崱35娆�", "beyongd": 3, "type": 2, "value": [35], "rewards": [[11, 10]], "pre": 3005}, "3007": {"id": 3007, "desc": "鎵崱40娆�", "beyongd": 3, "type": 2, "value": [40], "rewards": [[11, 10]], "pre": 3006}, "3008": {"id": 3008, "desc": "鎵崱45娆�", "beyongd": 3, "type": 2, "value": [45], "rewards": [[11, 10]], "pre": 3007}, "3009": {"id": 3009, "desc": "鎵崱50娆�", "beyongd": 3, "type": 2, "value": [50], "rewards": [[11, 10]], "pre": 3008}, "3010": {"id": 3010, "desc": "鎵崱55娆�", "beyongd": 3, "type": 2, "value": [55], "rewards": [[11, 10]], "pre": 3009}, "3011": {"id": 3011, "desc": "鎵崱60娆�", "beyongd": 3, "type": 2, "value": [60], "rewards": [[11, 10]], "pre": 3010}, "3012": {"id": 3012, "desc": "鎵崱65娆�", "beyongd": 3, "type": 2, "value": [65], "rewards": [[11, 10]], "pre": 3011}, "3013": {"id": 3013, "desc": "鎵崱70娆�", "beyongd": 3, "type": 2, "value": [70], "rewards": [[11, 10]], "pre": 3012}, "3014": {"id": 3014, "desc": "鎵崱75娆�", "beyongd": 3, "type": 2, "value": [75], "rewards": [[11, 10]], "pre": 3013}, "3015": {"id": 3015, "desc": "鎵崱80娆�", "beyongd": 3, "type": 2, "value": [80], "rewards": [[11, 10]], "pre": 3014}, "3016": {"id": 3016, "desc": "鎵崱85娆�", "beyongd": 3, "type": 2, "value": [85], "rewards": [[11, 10]], "pre": 3015}, "3017": {"id": 3017, "desc": "鎵崱90娆�", "beyongd": 3, "type": 2, "value": [90], "rewards": [[11, 10]], "pre": 3016}, "3018": {"id": 3018, "desc": "鎵崱95娆�", "beyongd": 3, "type": 2, "value": [95], "rewards": [[11, 10]], "pre": 3017}, "3019": {"id": 3019, "desc": "鎵崱100娆�", "beyongd": 3, "type": 2, "value": [100], "rewards": [[11, 10]], "pre": 3018}, "3020": {"id": 3020, "desc": "鎵崱105娆�", "beyongd": 3, "type": 2, "value": [105], "rewards": [[11, 10]], "pre": 3019}, "3021": {"id": 3021, "desc": "鎵崱110娆�", "beyongd": 3, "type": 2, "value": [110], "rewards": [[11, 10]], "pre": 3020}, "3022": {"id": 3022, "desc": "鎵崱115娆�", "beyongd": 3, "type": 2, "value": [115], "rewards": [[11, 10]], "pre": 3021}, "3023": {"id": 3023, "desc": "鎵崱120娆�", "beyongd": 3, "type": 2, "value": [120], "rewards": [[11, 10]], "pre": 3022}, "3024": {"id": 3024, "desc": "鎵崱125娆�", "beyongd": 3, "type": 2, "value": [125], "rewards": [[11, 10]], "pre": 3023}, "3025": {"id": 3025, "desc": "鎵崱130娆�", "beyongd": 3, "type": 2, "value": [130], "rewards": [[11, 10]], "pre": 3024}, "3026": {"id": 3026, "desc": "鎵崱135娆�", "beyongd": 3, "type": 2, "value": [135], "rewards": [[11, 10]], "pre": 3025}, "3027": {"id": 3027, "desc": "鎵崱140娆�", "beyongd": 3, "type": 2, "value": [140], "rewards": [[11, 10]], "pre": 3026}, "3028": {"id": 3028, "desc": "鎵崱145娆�", "beyongd": 3, "type": 2, "value": [145], "rewards": [[11, 10]], "pre": 3027}, "3029": {"id": 3029, "desc": "鎵崱150娆�", "beyongd": 3, "type": 2, "value": [150], "rewards": [[11, 10]], "pre": 3028}, "3030": {"id": 3030, "desc": "鎵崱155娆�", "beyongd": 3, "type": 2, "value": [155], "rewards": [[11, 10]], "pre": 3029}, "3031": {"id": 3031, "desc": "鎵崱160娆�", "beyongd": 3, "type": 2, "value": [160], "rewards": [[11, 10]], "pre": 3030}, "3032": {"id": 3032, "desc": "鎵崱165娆�", "beyongd": 3, "type": 2, "value": [165], "rewards": [[11, 10]], "pre": 3031}, "3033": {"id": 3033, "desc": "鎵崱170娆�", "beyongd": 3, "type": 2, "value": [170], "rewards": [[11, 10]], "pre": 3032}, "3034": {"id": 3034, "desc": "鎵崱175娆�", "beyongd": 3, "type": 2, "value": [175], "rewards": [[11, 10]], "pre": 3033}, "3035": {"id": 3035, "desc": "鎵崱180娆�", "beyongd": 3, "type": 2, "value": [180], "rewards": [[11, 10]], "pre": 3034}, "3036": {"id": 3036, "desc": "鎵崱185娆�", "beyongd": 3, "type": 2, "value": [185], "rewards": [[11, 10]], "pre": 3035}, "3037": {"id": 3037, "desc": "鎵崱190娆�", "beyongd": 3, "type": 2, "value": [190], "rewards": [[11, 10]], "pre": 3036}, "3038": {"id": 3038, "desc": "鎵崱195娆�", "beyongd": 3, "type": 2, "value": [195], "rewards": [[11, 10]], "pre": 3037}, "3039": {"id": 3039, "desc": "鎵崱200娆�", "beyongd": 3, "type": 2, "value": [200], "rewards": [[11, 10]], "pre": 3038}, "3040": {"id": 3040, "desc": "鎵崱205娆�", "beyongd": 3, "type": 2, "value": [205], "rewards": [[11, 10]], "pre": 3039}, "3041": {"id": 3041, "desc": "鎵崱210娆�", "beyongd": 3, "type": 2, "value": [210], "rewards": [[11, 10]], "pre": 3040}, "3042": {"id": 3042, "desc": "鎵崱215娆�", "beyongd": 3, "type": 2, "value": [215], "rewards": [[11, 10]], "pre": 3041}, "3043": {"id": 3043, "desc": "鎵崱220娆�", "beyongd": 3, "type": 2, "value": [220], "rewards": [[11, 10]], "pre": 3042}, "3044": {"id": 3044, "desc": "鎵崱225娆�", "beyongd": 3, "type": 2, "value": [225], "rewards": [[11, 10]], "pre": 3043}, "3045": {"id": 3045, "desc": "鎵崱230娆�", "beyongd": 3, "type": 2, "value": [230], "rewards": [[11, 10]], "pre": 3044}, "3046": {"id": 3046, "desc": "鎵崱235娆�", "beyongd": 3, "type": 2, "value": [235], "rewards": [[11, 10]], "pre": 3045}, "3047": {"id": 3047, "desc": "鎵崱240娆�", "beyongd": 3, "type": 2, "value": [240], "rewards": [[11, 10]], "pre": 3046}, "3048": {"id": 3048, "desc": "鎵崱245娆�", "beyongd": 3, "type": 2, "value": [245], "rewards": [[11, 10]], "pre": 3047}, "3049": {"id": 3049, "desc": "鎵崱250娆�", "beyongd": 3, "type": 2, "value": [250], "rewards": [[11, 10]], "pre": 3048}, "3050": {"id": 3050, "desc": "鎵崱255娆�", "beyongd": 3, "type": 2, "value": [255], "rewards": [[11, 10]], "pre": 3049}, "3051": {"id": 3051, "desc": "鎵崱260娆�", "beyongd": 3, "type": 2, "value": [260], "rewards": [[11, 10]], "pre": 3050}, "3052": {"id": 3052, "desc": "鎵崱265娆�", "beyongd": 3, "type": 2, "value": [265], "rewards": [[11, 10]], "pre": 3051}, "3053": {"id": 3053, "desc": "鎵崱270娆�", "beyongd": 3, "type": 2, "value": [270], "rewards": [[11, 10]], "pre": 3052}, "3054": {"id": 3054, "desc": "鎵崱275娆�", "beyongd": 3, "type": 2, "value": [275], "rewards": [[11, 10]], "pre": 3053}, "3055": {"id": 3055, "desc": "鎵崱280娆�", "beyongd": 3, "type": 2, "value": [280], "rewards": [[11, 10]], "pre": 3054}, "3056": {"id": 3056, "desc": "鎵崱285娆�", "beyongd": 3, "type": 2, "value": [285], "rewards": [[11, 10]], "pre": 3055}, "3057": {"id": 3057, "desc": "鎵崱290娆�", "beyongd": 3, "type": 2, "value": [290], "rewards": [[11, 10]], "pre": 3056}, "3058": {"id": 3058, "desc": "鎵崱295娆�", "beyongd": 3, "type": 2, "value": [295], "rewards": [[11, 10]], "pre": 3057}, "3059": {"id": 3059, "desc": "鎵崱300娆�", "beyongd": 3, "type": 2, "value": [300], "rewards": [[11, 10]], "pre": 3058}, "3060": {"id": 3060, "desc": "鎵崱305娆�", "beyongd": 3, "type": 2, "value": [305], "rewards": [[11, 10]], "pre": 3059}, "3061": {"id": 3061, "desc": "鎵崱310娆�", "beyongd": 3, "type": 2, "value": [310], "rewards": [[11, 10]], "pre": 3060}, "3062": {"id": 3062, "desc": "鎵崱315娆�", "beyongd": 3, "type": 2, "value": [315], "rewards": [[11, 10]], "pre": 3061}, "3063": {"id": 3063, "desc": "鎵崱320娆�", "beyongd": 3, "type": 2, "value": [320], "rewards": [[11, 10]], "pre": 3062}, "3064": {"id": 3064, "desc": "鎵崱325娆�", "beyongd": 3, "type": 2, "value": [325], "rewards": [[11, 10]], "pre": 3063}, "3065": {"id": 3065, "desc": "鎵崱330娆�", "beyongd": 3, "type": 2, "value": [330], "rewards": [[11, 10]], "pre": 3064}, "3066": {"id": 3066, "desc": "鎵崱335娆�", "beyongd": 3, "type": 2, "value": [335], "rewards": [[11, 10]], "pre": 3065}, "3067": {"id": 3067, "desc": "鎵崱340娆�", "beyongd": 3, "type": 2, "value": [340], "rewards": [[11, 10]], "pre": 3066}, "3068": {"id": 3068, "desc": "鎵崱345娆�", "beyongd": 3, "type": 2, "value": [345], "rewards": [[11, 10]], "pre": 3067}, "3069": {"id": 3069, "desc": "鎵崱350娆�", "beyongd": 3, "type": 2, "value": [350], "rewards": [[11, 10]], "pre": 3068}, "3070": {"id": 3070, "desc": "鎵崱355娆�", "beyongd": 3, "type": 2, "value": [355], "rewards": [[11, 10]], "pre": 3069}, "3071": {"id": 3071, "desc": "鎵崱360娆�", "beyongd": 3, "type": 2, "value": [360], "rewards": [[11, 10]], "pre": 3070}, "3072": {"id": 3072, "desc": "鎵崱365娆�", "beyongd": 3, "type": 2, "value": [365], "rewards": [[11, 10]], "pre": 3071}, "3073": {"id": 3073, "desc": "鎵崱370娆�", "beyongd": 3, "type": 2, "value": [370], "rewards": [[11, 10]], "pre": 3072}, "3074": {"id": 3074, "desc": "鎵崱375娆�", "beyongd": 3, "type": 2, "value": [375], "rewards": [[11, 10]], "pre": 3073}, "3075": {"id": 3075, "desc": "鎵崱380娆�", "beyongd": 3, "type": 2, "value": [380], "rewards": [[11, 10]], "pre": 3074}, "3076": {"id": 3076, "desc": "鎵崱385娆�", "beyongd": 3, "type": 2, "value": [385], "rewards": [[11, 10]], "pre": 3075}, "3077": {"id": 3077, "desc": "鎵崱390娆�", "beyongd": 3, "type": 2, "value": [390], "rewards": [[11, 10]], "pre": 3076}, "3078": {"id": 3078, "desc": "鎵崱395娆�", "beyongd": 3, "type": 2, "value": [395], "rewards": [[11, 10]], "pre": 3077}, "3079": {"id": 3079, "desc": "鎵崱400娆�", "beyongd": 3, "type": 2, "value": [400], "rewards": [[11, 10]], "pre": 3078}, "3080": {"id": 3080, "desc": "鎵崱405娆�", "beyongd": 3, "type": 2, "value": [405], "rewards": [[11, 10]], "pre": 3079}, "3081": {"id": 3081, "desc": "鎵崱410娆�", "beyongd": 3, "type": 2, "value": [410], "rewards": [[11, 10]], "pre": 3080}, "3082": {"id": 3082, "desc": "鎵崱415娆�", "beyongd": 3, "type": 2, "value": [415], "rewards": [[11, 10]], "pre": 3081}, "3083": {"id": 3083, "desc": "鎵崱420娆�", "beyongd": 3, "type": 2, "value": [420], "rewards": [[11, 10]], "pre": 3082}, "3084": {"id": 3084, "desc": "鎵崱425娆�", "beyongd": 3, "type": 2, "value": [425], "rewards": [[11, 10]], "pre": 3083}, "3085": {"id": 3085, "desc": "鎵崱430娆�", "beyongd": 3, "type": 2, "value": [430], "rewards": [[11, 10]], "pre": 3084}, "3086": {"id": 3086, "desc": "鎵崱435娆�", "beyongd": 3, "type": 2, "value": [435], "rewards": [[11, 10]], "pre": 3085}, "3087": {"id": 3087, "desc": "鎵崱440娆�", "beyongd": 3, "type": 2, "value": [440], "rewards": [[11, 10]], "pre": 3086}, "3088": {"id": 3088, "desc": "鎵崱445娆�", "beyongd": 3, "type": 2, "value": [445], "rewards": [[11, 10]], "pre": 3087}, "3089": {"id": 3089, "desc": "鎵崱450娆�", "beyongd": 3, "type": 2, "value": [450], "rewards": [[11, 10]], "pre": 3088}, "4000": {"id": 4000, "desc": "娑堣€楅捇鐭�700", "beyongd": 3, "type": 5, "value": [700], "rewards": [[11, 10]]}, "4001": {"id": 4001, "desc": "娑堣€楅捇鐭�1400", "beyongd": 3, "type": 5, "value": [1400], "rewards": [[11, 10]], "pre": 4000}, "4002": {"id": 4002, "desc": "娑堣€楅捇鐭�2100", "beyongd": 3, "type": 5, "value": [2100], "rewards": [[11, 10]], "pre": 4001}, "4003": {"id": 4003, "desc": "娑堣€楅捇鐭�2800", "beyongd": 3, "type": 5, "value": [2800], "rewards": [[11, 10]], "pre": 4002}, "4004": {"id": 4004, "desc": "娑堣€楅捇鐭�3500", "beyongd": 3, "type": 5, "value": [3500], "rewards": [[11, 10]], "pre": 4003}, "4005": {"id": 4005, "desc": "娑堣€楅捇鐭�4200", "beyongd": 3, "type": 5, "value": [4200], "rewards": [[11, 10]], "pre": 4004}, "4006": {"id": 4006, "desc": "娑堣€楅捇鐭�4900", "beyongd": 3, "type": 5, "value": [4900], "rewards": [[11, 10]], "pre": 4005}, "4007": {"id": 4007, "desc": "娑堣€楅捇鐭�5600", "beyongd": 3, "type": 5, "value": [5600], "rewards": [[11, 10]], "pre": 4006}, "4008": {"id": 4008, "desc": "娑堣€楅捇鐭�6300", "beyongd": 3, "type": 5, "value": [6300], "rewards": [[11, 10]], "pre": 4007}, "4009": {"id": 4009, "desc": "娑堣€楅捇鐭�7000", "beyongd": 3, "type": 5, "value": [7000], "rewards": [[11, 10]], "pre": 4008}, "4010": {"id": 4010, "desc": "娑堣€楅捇鐭�7700", "beyongd": 3, "type": 5, "value": [7700], "rewards": [[11, 10]], "pre": 4009}, "4011": {"id": 4011, "desc": "娑堣€楅捇鐭�8400", "beyongd": 3, "type": 5, "value": [8400], "rewards": [[11, 10]], "pre": 4010}, "4012": {"id": 4012, "desc": "娑堣€楅捇鐭�9100", "beyongd": 3, "type": 5, "value": [9100], "rewards": [[11, 10]], "pre": 4011}, "4013": {"id": 4013, "desc": "娑堣€楅捇鐭�9800", "beyongd": 3, "type": 5, "value": [9800], "rewards": [[11, 10]], "pre": 4012}, "4014": {"id": 4014, "desc": "娑堣€楅捇鐭�10500", "beyongd": 3, "type": 5, "value": [10500], "rewards": [[11, 10]], "pre": 4013}, "4015": {"id": 4015, "desc": "娑堣€楅捇鐭�11200", "beyongd": 3, "type": 5, "value": [11200], "rewards": [[11, 10]], "pre": 4014}, "4016": {"id": 4016, "desc": "娑堣€楅捇鐭�11900", "beyongd": 3, "type": 5, "value": [11900], "rewards": [[11, 10]], "pre": 4015}, "4017": {"id": 4017, "desc": "娑堣€楅捇鐭�12600", "beyongd": 3, "type": 5, "value": [12600], "rewards": [[11, 10]], "pre": 4016}, "4018": {"id": 4018, "desc": "娑堣€楅捇鐭�13300", "beyongd": 3, "type": 5, "value": [13300], "rewards": [[11, 10]], "pre": 4017}, "4019": {"id": 4019, "desc": "娑堣€楅捇鐭�14000", "beyongd": 3, "type": 5, "value": [14000], "rewards": [[11, 10]], "pre": 4018}, "4020": {"id": 4020, "desc": "娑堣€楅捇鐭�14700", "beyongd": 3, "type": 5, "value": [14700], "rewards": [[11, 10]], "pre": 4019}, "4021": {"id": 4021, "desc": "娑堣€楅捇鐭�15400", "beyongd": 3, "type": 5, "value": [15400], "rewards": [[11, 10]], "pre": 4020}, "4022": {"id": 4022, "desc": "娑堣€楅捇鐭�16100", "beyongd": 3, "type": 5, "value": [16100], "rewards": [[11, 10]], "pre": 4021}, "4023": {"id": 4023, "desc": "娑堣€楅捇鐭�16800", "beyongd": 3, "type": 5, "value": [16800], "rewards": [[11, 10]], "pre": 4022}, "4024": {"id": 4024, "desc": "娑堣€楅捇鐭�17500", "beyongd": 3, "type": 5, "value": [17500], "rewards": [[11, 10]], "pre": 4023}, "4025": {"id": 4025, "desc": "娑堣€楅捇鐭�18200", "beyongd": 3, "type": 5, "value": [18200], "rewards": [[11, 10]], "pre": 4024}, "4026": {"id": 4026, "desc": "娑堣€楅捇鐭�18900", "beyongd": 3, "type": 5, "value": [18900], "rewards": [[11, 10]], "pre": 4025}, "4027": {"id": 4027, "desc": "娑堣€楅捇鐭�19600", "beyongd": 3, "type": 5, "value": [19600], "rewards": [[11, 10]], "pre": 4026}, "4028": {"id": 4028, "desc": "娑堣€楅捇鐭�20300", "beyongd": 3, "type": 5, "value": [20300], "rewards": [[11, 10]], "pre": 4027}, "4029": {"id": 4029, "desc": "娑堣€楅捇鐭�21000", "beyongd": 3, "type": 5, "value": [21000], "rewards": [[11, 10]], "pre": 4028}, "4030": {"id": 4030, "desc": "娑堣€楅捇鐭�21700", "beyongd": 3, "type": 5, "value": [21700], "rewards": [[11, 10]], "pre": 4029}, "4031": {"id": 4031, "desc": "娑堣€楅捇鐭�22400", "beyongd": 3, "type": 5, "value": [22400], "rewards": [[11, 10]], "pre": 4030}, "4032": {"id": 4032, "desc": "娑堣€楅捇鐭�23100", "beyongd": 3, "type": 5, "value": [23100], "rewards": [[11, 10]], "pre": 4031}, "4033": {"id": 4033, "desc": "娑堣€楅捇鐭�23800", "beyongd": 3, "type": 5, "value": [23800], "rewards": [[11, 10]], "pre": 4032}, "4034": {"id": 4034, "desc": "娑堣€楅捇鐭�24500", "beyongd": 3, "type": 5, "value": [24500], "rewards": [[11, 10]], "pre": 4033}, "4035": {"id": 4035, "desc": "娑堣€楅捇鐭�25200", "beyongd": 3, "type": 5, "value": [25200], "rewards": [[11, 10]], "pre": 4034}, "4036": {"id": 4036, "desc": "娑堣€楅捇鐭�25900", "beyongd": 3, "type": 5, "value": [25900], "rewards": [[11, 10]], "pre": 4035}, "4037": {"id": 4037, "desc": "娑堣€楅捇鐭�26600", "beyongd": 3, "type": 5, "value": [26600], "rewards": [[11, 10]], "pre": 4036}, "4038": {"id": 4038, "desc": "娑堣€楅捇鐭�27300", "beyongd": 3, "type": 5, "value": [27300], "rewards": [[11, 10]], "pre": 4037}, "4039": {"id": 4039, "desc": "娑堣€楅捇鐭�28000", "beyongd": 3, "type": 5, "value": [28000], "rewards": [[11, 10]], "pre": 4038}, "4040": {"id": 4040, "desc": "娑堣€楅捇鐭�28700", "beyongd": 3, "type": 5, "value": [28700], "rewards": [[11, 10]], "pre": 4039}, "4041": {"id": 4041, "desc": "娑堣€楅捇鐭�29400", "beyongd": 3, "type": 5, "value": [29400], "rewards": [[11, 10]], "pre": 4040}, "4042": {"id": 4042, "desc": "娑堣€楅捇鐭�30100", "beyongd": 3, "type": 5, "value": [30100], "rewards": [[11, 10]], "pre": 4041}, "4043": {"id": 4043, "desc": "娑堣€楅捇鐭�30800", "beyongd": 3, "type": 5, "value": [30800], "rewards": [[11, 10]], "pre": 4042}, "4044": {"id": 4044, "desc": "娑堣€楅捇鐭�31500", "beyongd": 3, "type": 5, "value": [31500], "rewards": [[11, 10]], "pre": 4043}, "4045": {"id": 4045, "desc": "娑堣€楅捇鐭�32200", "beyongd": 3, "type": 5, "value": [32200], "rewards": [[11, 10]], "pre": 4044}, "4046": {"id": 4046, "desc": "娑堣€楅捇鐭�32900", "beyongd": 3, "type": 5, "value": [32900], "rewards": [[11, 10]], "pre": 4045}, "4047": {"id": 4047, "desc": "娑堣€楅捇鐭�33600", "beyongd": 3, "type": 5, "value": [33600], "rewards": [[11, 10]], "pre": 4046}, "4048": {"id": 4048, "desc": "娑堣€楅捇鐭�34300", "beyongd": 3, "type": 5, "value": [34300], "rewards": [[11, 10]], "pre": 4047}, "4049": {"id": 4049, "desc": "娑堣€楅捇鐭�35000", "beyongd": 3, "type": 5, "value": [35000], "rewards": [[11, 10]], "pre": 4048}, "4050": {"id": 4050, "desc": "娑堣€楅捇鐭�35700", "beyongd": 3, "type": 5, "value": [35700], "rewards": [[11, 10]], "pre": 4049}, "4051": {"id": 4051, "desc": "娑堣€楅捇鐭�36400", "beyongd": 3, "type": 5, "value": [36400], "rewards": [[11, 10]], "pre": 4050}, "4052": {"id": 4052, "desc": "娑堣€楅捇鐭�37100", "beyongd": 3, "type": 5, "value": [37100], "rewards": [[11, 10]], "pre": 4051}, "4053": {"id": 4053, "desc": "娑堣€楅捇鐭�37800", "beyongd": 3, "type": 5, "value": [37800], "rewards": [[11, 10]], "pre": 4052}, "4054": {"id": 4054, "desc": "娑堣€楅捇鐭�38500", "beyongd": 3, "type": 5, "value": [38500], "rewards": [[11, 10]], "pre": 4053}, "4055": {"id": 4055, "desc": "娑堣€楅捇鐭�39200", "beyongd": 3, "type": 5, "value": [39200], "rewards": [[11, 10]], "pre": 4054}, "4056": {"id": 4056, "desc": "娑堣€楅捇鐭�39900", "beyongd": 3, "type": 5, "value": [39900], "rewards": [[11, 10]], "pre": 4055}, "4057": {"id": 4057, "desc": "娑堣€楅捇鐭�40600", "beyongd": 3, "type": 5, "value": [40600], "rewards": [[11, 10]], "pre": 4056}, "4058": {"id": 4058, "desc": "娑堣€楅捇鐭�41300", "beyongd": 3, "type": 5, "value": [41300], "rewards": [[11, 10]], "pre": 4057}, "4059": {"id": 4059, "desc": "娑堣€楅捇鐭�42000", "beyongd": 3, "type": 5, "value": [42000], "rewards": [[11, 10]], "pre": 4058}, "4060": {"id": 4060, "desc": "娑堣€楅捇鐭�42700", "beyongd": 3, "type": 5, "value": [42700], "rewards": [[11, 10]], "pre": 4059}, "4061": {"id": 4061, "desc": "娑堣€楅捇鐭�1000", "beyongd": 3, "type": 5, "value": [1000], "rewards": [[11, 10]]}, "4062": {"id": 4062, "desc": "娑堣€楅捇鐭�2000", "beyongd": 3, "type": 5, "value": [2000], "rewards": [[11, 10]], "pre": 4061}, "4063": {"id": 4063, "desc": "娑堣€楅捇鐭�3000", "beyongd": 3, "type": 5, "value": [3000], "rewards": [[11, 10]], "pre": 4062}, "4064": {"id": 4064, "desc": "娑堣€楅捇鐭�4000", "beyongd": 3, "type": 5, "value": [4000], "rewards": [[11, 10]], "pre": 4063}, "4065": {"id": 4065, "desc": "娑堣€楅捇鐭�5000", "beyongd": 3, "type": 5, "value": [5000], "rewards": [[11, 10]], "pre": 4064}, "4066": {"id": 4066, "desc": "娑堣€楅捇鐭�6000", "beyongd": 3, "type": 5, "value": [6000], "rewards": [[11, 10]], "pre": 4065}, "4067": {"id": 4067, "desc": "娑堣€楅捇鐭�7000", "beyongd": 3, "type": 5, "value": [7000], "rewards": [[11, 10]], "pre": 4066}, "4068": {"id": 4068, "desc": "娑堣€楅捇鐭�8000", "beyongd": 3, "type": 5, "value": [8000], "rewards": [[11, 10]], "pre": 4067}, "4069": {"id": 4069, "desc": "娑堣€楅捇鐭�9000", "beyongd": 3, "type": 5, "value": [9000], "rewards": [[11, 10]], "pre": 4068}, "4070": {"id": 4070, "desc": "娑堣€楅捇鐭�10000", "beyongd": 3, "type": 5, "value": [10000], "rewards": [[11, 10]], "pre": 4069}, "4071": {"id": 4071, "desc": "娑堣€楅捇鐭�11000", "beyongd": 3, "type": 5, "value": [11000], "rewards": [[11, 10]], "pre": 4070}, "4072": {"id": 4072, "desc": "娑堣€楅捇鐭�12000", "beyongd": 3, "type": 5, "value": [12000], "rewards": [[11, 10]], "pre": 4071}, "4073": {"id": 4073, "desc": "娑堣€楅捇鐭�13000", "beyongd": 3, "type": 5, "value": [13000], "rewards": [[11, 10]], "pre": 4072}, "4074": {"id": 4074, "desc": "娑堣€楅捇鐭�14000", "beyongd": 3, "type": 5, "value": [14000], "rewards": [[11, 10]], "pre": 4073}, "4075": {"id": 4075, "desc": "娑堣€楅捇鐭�15000", "beyongd": 3, "type": 5, "value": [15000], "rewards": [[11, 10]], "pre": 4074}, "4076": {"id": 4076, "desc": "娑堣€楅捇鐭�16000", "beyongd": 3, "type": 5, "value": [16000], "rewards": [[11, 10]], "pre": 4075}, "4077": {"id": 4077, "desc": "娑堣€楅捇鐭�17000", "beyongd": 3, "type": 5, "value": [17000], "rewards": [[11, 10]], "pre": 4076}, "4078": {"id": 4078, "desc": "娑堣€楅捇鐭�18000", "beyongd": 3, "type": 5, "value": [18000], "rewards": [[11, 10]], "pre": 4077}, "4079": {"id": 4079, "desc": "娑堣€楅捇鐭�19000", "beyongd": 3, "type": 5, "value": [19000], "rewards": [[11, 10]], "pre": 4078}, "4080": {"id": 4080, "desc": "娑堣€楅捇鐭�20000", "beyongd": 3, "type": 5, "value": [20000], "rewards": [[11, 10]], "pre": 4079}, "4081": {"id": 4081, "desc": "娑堣€楅捇鐭�21000", "beyongd": 3, "type": 5, "value": [21000], "rewards": [[11, 10]], "pre": 4080}, "4082": {"id": 4082, "desc": "娑堣€楅捇鐭�22000", "beyongd": 3, "type": 5, "value": [22000], "rewards": [[11, 10]], "pre": 4081}, "4083": {"id": 4083, "desc": "娑堣€楅捇鐭�23000", "beyongd": 3, "type": 5, "value": [23000], "rewards": [[11, 10]], "pre": 4082}, "4084": {"id": 4084, "desc": "娑堣€楅捇鐭�24000", "beyongd": 3, "type": 5, "value": [24000], "rewards": [[11, 10]], "pre": 4083}, "4085": {"id": 4085, "desc": "娑堣€楅捇鐭�25000", "beyongd": 3, "type": 5, "value": [25000], "rewards": [[11, 10]], "pre": 4084}, "4086": {"id": 4086, "desc": "娑堣€楅捇鐭�26000", "beyongd": 3, "type": 5, "value": [26000], "rewards": [[11, 10]], "pre": 4085}, "4087": {"id": 4087, "desc": "娑堣€楅捇鐭�27000", "beyongd": 3, "type": 5, "value": [27000], "rewards": [[11, 10]], "pre": 4086}, "4088": {"id": 4088, "desc": "娑堣€楅捇鐭�28000", "beyongd": 3, "type": 5, "value": [28000], "rewards": [[11, 10]], "pre": 4087}, "4089": {"id": 4089, "desc": "娑堣€楅捇鐭�29000", "beyongd": 3, "type": 5, "value": [29000], "rewards": [[11, 10]], "pre": 4088}, "4090": {"id": 4090, "desc": "娑堣€楅捇鐭�30000", "beyongd": 3, "type": 5, "value": [30000], "rewards": [[11, 10]], "pre": 4089}, "4091": {"id": 4091, "desc": "娑堣€楅捇鐭�31000", "beyongd": 3, "type": 5, "value": [31000], "rewards": [[11, 10]], "pre": 4090}, "4092": {"id": 4092, "desc": "娑堣€楅捇鐭�32000", "beyongd": 3, "type": 5, "value": [32000], "rewards": [[11, 10]], "pre": 4091}, "4093": {"id": 4093, "desc": "娑堣€楅捇鐭�33000", "beyongd": 3, "type": 5, "value": [33000], "rewards": [[11, 10]], "pre": 4092}, "4094": {"id": 4094, "desc": "娑堣€楅捇鐭�34000", "beyongd": 3, "type": 5, "value": [34000], "rewards": [[11, 10]], "pre": 4093}, "4095": {"id": 4095, "desc": "娑堣€楅捇鐭�35000", "beyongd": 3, "type": 5, "value": [35000], "rewards": [[11, 10]], "pre": 4094}, "4096": {"id": 4096, "desc": "娑堣€楅捇鐭�36000", "beyongd": 3, "type": 5, "value": [36000], "rewards": [[11, 10]], "pre": 4095}, "4097": {"id": 4097, "desc": "娑堣€楅捇鐭�37000", "beyongd": 3, "type": 5, "value": [37000], "rewards": [[11, 10]], "pre": 4096}, "4098": {"id": 4098, "desc": "娑堣€楅捇鐭�38000", "beyongd": 3, "type": 5, "value": [38000], "rewards": [[11, 10]], "pre": 4097}, "4099": {"id": 4099, "desc": "娑堣€楅捇鐭�39000", "beyongd": 3, "type": 5, "value": [39000], "rewards": [[11, 10]], "pre": 4098}, "4100": {"id": 4100, "desc": "娑堣€楅捇鐭�40000", "beyongd": 3, "type": 5, "value": [40000], "rewards": [[11, 10]], "pre": 4099}, "4101": {"id": 4101, "desc": "娑堣€楅捇鐭�41000", "beyongd": 3, "type": 5, "value": [41000], "rewards": [[11, 10]], "pre": 4100}, "4102": {"id": 4102, "desc": "娑堣€楅捇鐭�42000", "beyongd": 3, "type": 5, "value": [42000], "rewards": [[11, 10]], "pre": 4101}, "4103": {"id": 4103, "desc": "娑堣€楅捇鐭�43000", "beyongd": 3, "type": 5, "value": [43000], "rewards": [[11, 10]], "pre": 4102}, "4104": {"id": 4104, "desc": "娑堣€楅捇鐭�44000", "beyongd": 3, "type": 5, "value": [44000], "rewards": [[11, 10]], "pre": 4103}, "4105": {"id": 4105, "desc": "娑堣€楅捇鐭�45000", "beyongd": 3, "type": 5, "value": [45000], "rewards": [[11, 10]], "pre": 4104}, "4106": {"id": 4106, "desc": "娑堣€楅捇鐭�46000", "beyongd": 3, "type": 5, "value": [46000], "rewards": [[11, 10]], "pre": 4105}, "4107": {"id": 4107, "desc": "娑堣€楅捇鐭�47000", "beyongd": 3, "type": 5, "value": [47000], "rewards": [[11, 10]], "pre": 4106}, "4108": {"id": 4108, "desc": "娑堣€楅捇鐭�48000", "beyongd": 3, "type": 5, "value": [48000], "rewards": [[11, 10]], "pre": 4107}, "4109": {"id": 4109, "desc": "娑堣€楅捇鐭�49000", "beyongd": 3, "type": 5, "value": [49000], "rewards": [[11, 10]], "pre": 4108}, "4110": {"id": 4110, "desc": "娑堣€楅捇鐭�50000", "beyongd": 3, "type": 5, "value": [50000], "rewards": [[11, 10]], "pre": 4109}, "4111": {"id": 4111, "desc": "娑堣€楅捇鐭�51000", "beyongd": 3, "type": 5, "value": [51000], "rewards": [[11, 10]], "pre": 4110}, "4112": {"id": 4112, "desc": "娑堣€楅捇鐭�52000", "beyongd": 3, "type": 5, "value": [52000], "rewards": [[11, 10]], "pre": 4111}, "4113": {"id": 4113, "desc": "娑堣€楅捇鐭�53000", "beyongd": 3, "type": 5, "value": [53000], "rewards": [[11, 10]], "pre": 4112}, "4114": {"id": 4114, "desc": "娑堣€楅捇鐭�54000", "beyongd": 3, "type": 5, "value": [54000], "rewards": [[11, 10]], "pre": 4113}, "4115": {"id": 4115, "desc": "娑堣€楅捇鐭�55000", "beyongd": 3, "type": 5, "value": [55000], "rewards": [[11, 10]], "pre": 4114}, "4116": {"id": 4116, "desc": "娑堣€楅捇鐭�56000", "beyongd": 3, "type": 5, "value": [56000], "rewards": [[11, 10]], "pre": 4115}, "4117": {"id": 4117, "desc": "娑堣€楅捇鐭�57000", "beyongd": 3, "type": 5, "value": [57000], "rewards": [[11, 10]], "pre": 4116}, "4118": {"id": 4118, "desc": "娑堣€楅捇鐭�58000", "beyongd": 3, "type": 5, "value": [58000], "rewards": [[11, 10]], "pre": 4117}, "4119": {"id": 4119, "desc": "娑堣€楅捇鐭�59000", "beyongd": 3, "type": 5, "value": [59000], "rewards": [[11, 10]], "pre": 4118}, "4120": {"id": 4120, "desc": "娑堣€楅捇鐭�60000", "beyongd": 3, "type": 5, "value": [60000], "rewards": [[11, 10]], "pre": 4119}, "5000": {"id": 5000, "desc": "娑堣€楅噾甯�7000", "beyongd": 3, "type": 6, "value": [7000], "rewards": [[11, 10]]}, "5001": {"id": 5001, "desc": "娑堣€楅噾甯�14000", "beyongd": 3, "type": 6, "value": [14000], "rewards": [[11, 10]], "pre": 5000}, "5002": {"id": 5002, "desc": "娑堣€楅噾甯�21000", "beyongd": 3, "type": 6, "value": [21000], "rewards": [[11, 10]], "pre": 5001}, "5003": {"id": 5003, "desc": "娑堣€楅噾甯�28000", "beyongd": 3, "type": 6, "value": [28000], "rewards": [[11, 10]], "pre": 5002}, "5004": {"id": 5004, "desc": "娑堣€楅噾甯�35000", "beyongd": 3, "type": 6, "value": [35000], "rewards": [[11, 10]], "pre": 5003}, "5005": {"id": 5005, "desc": "娑堣€楅噾甯�42000", "beyongd": 3, "type": 6, "value": [42000], "rewards": [[11, 10]], "pre": 5004}, "5006": {"id": 5006, "desc": "娑堣€楅噾甯�49000", "beyongd": 3, "type": 6, "value": [49000], "rewards": [[11, 10]], "pre": 5005}, "5007": {"id": 5007, "desc": "娑堣€楅噾甯�56000", "beyongd": 3, "type": 6, "value": [56000], "rewards": [[11, 10]], "pre": 5006}, "5008": {"id": 5008, "desc": "娑堣€楅噾甯�63000", "beyongd": 3, "type": 6, "value": [63000], "rewards": [[11, 10]], "pre": 5007}, "5009": {"id": 5009, "desc": "娑堣€楅噾甯�70000", "beyongd": 3, "type": 6, "value": [70000], "rewards": [[11, 10]], "pre": 5008}, "5010": {"id": 5010, "desc": "娑堣€楅噾甯�77000", "beyongd": 3, "type": 6, "value": [77000], "rewards": [[11, 10]], "pre": 5009}, "5011": {"id": 5011, "desc": "娑堣€楅噾甯�84000", "beyongd": 3, "type": 6, "value": [84000], "rewards": [[11, 10]], "pre": 5010}, "5012": {"id": 5012, "desc": "娑堣€楅噾甯�91000", "beyongd": 3, "type": 6, "value": [91000], "rewards": [[11, 10]], "pre": 5011}, "5013": {"id": 5013, "desc": "娑堣€楅噾甯�98000", "beyongd": 3, "type": 6, "value": [98000], "rewards": [[11, 10]], "pre": 5012}, "5014": {"id": 5014, "desc": "娑堣€楅噾甯�105000", "beyongd": 3, "type": 6, "value": [105000], "rewards": [[11, 10]], "pre": 5013}, "5015": {"id": 5015, "desc": "娑堣€楅噾甯�112000", "beyongd": 3, "type": 6, "value": [112000], "rewards": [[11, 10]], "pre": 5014}, "5016": {"id": 5016, "desc": "娑堣€楅噾甯�119000", "beyongd": 3, "type": 6, "value": [119000], "rewards": [[11, 10]], "pre": 5015}, "5017": {"id": 5017, "desc": "娑堣€楅噾甯�126000", "beyongd": 3, "type": 6, "value": [126000], "rewards": [[11, 10]], "pre": 5016}, "5018": {"id": 5018, "desc": "娑堣€楅噾甯�133000", "beyongd": 3, "type": 6, "value": [133000], "rewards": [[11, 10]], "pre": 5017}, "5019": {"id": 5019, "desc": "娑堣€楅噾甯�140000", "beyongd": 3, "type": 6, "value": [140000], "rewards": [[11, 10]], "pre": 5018}, "5020": {"id": 5020, "desc": "娑堣€楅噾甯�147000", "beyongd": 3, "type": 6, "value": [147000], "rewards": [[11, 10]], "pre": 5019}, "5021": {"id": 5021, "desc": "娑堣€楅噾甯�154000", "beyongd": 3, "type": 6, "value": [154000], "rewards": [[11, 10]], "pre": 5020}, "5022": {"id": 5022, "desc": "娑堣€楅噾甯�161000", "beyongd": 3, "type": 6, "value": [161000], "rewards": [[11, 10]], "pre": 5021}, "5023": {"id": 5023, "desc": "娑堣€楅噾甯�168000", "beyongd": 3, "type": 6, "value": [168000], "rewards": [[11, 10]], "pre": 5022}, "5024": {"id": 5024, "desc": "娑堣€楅噾甯�175000", "beyongd": 3, "type": 6, "value": [175000], "rewards": [[11, 10]], "pre": 5023}, "5025": {"id": 5025, "desc": "娑堣€楅噾甯�182000", "beyongd": 3, "type": 6, "value": [182000], "rewards": [[11, 10]], "pre": 5024}, "5026": {"id": 5026, "desc": "娑堣€楅噾甯�189000", "beyongd": 3, "type": 6, "value": [189000], "rewards": [[11, 10]], "pre": 5025}, "5027": {"id": 5027, "desc": "娑堣€楅噾甯�196000", "beyongd": 3, "type": 6, "value": [196000], "rewards": [[11, 10]], "pre": 5026}, "5028": {"id": 5028, "desc": "娑堣€楅噾甯�203000", "beyongd": 3, "type": 6, "value": [203000], "rewards": [[11, 10]], "pre": 5027}, "5029": {"id": 5029, "desc": "娑堣€楅噾甯�210000", "beyongd": 3, "type": 6, "value": [210000], "rewards": [[11, 10]], "pre": 5028}, "5030": {"id": 5030, "desc": "娑堣€楅噾甯�217000", "beyongd": 3, "type": 6, "value": [217000], "rewards": [[11, 10]], "pre": 5029}, "5031": {"id": 5031, "desc": "娑堣€楅噾甯�224000", "beyongd": 3, "type": 6, "value": [224000], "rewards": [[11, 10]], "pre": 5030}, "5032": {"id": 5032, "desc": "娑堣€楅噾甯�231000", "beyongd": 3, "type": 6, "value": [231000], "rewards": [[11, 10]], "pre": 5031}, "5033": {"id": 5033, "desc": "娑堣€楅噾甯�238000", "beyongd": 3, "type": 6, "value": [238000], "rewards": [[11, 10]], "pre": 5032}, "5034": {"id": 5034, "desc": "娑堣€楅噾甯�245000", "beyongd": 3, "type": 6, "value": [245000], "rewards": [[11, 10]], "pre": 5033}, "5035": {"id": 5035, "desc": "娑堣€楅噾甯�252000", "beyongd": 3, "type": 6, "value": [252000], "rewards": [[11, 10]], "pre": 5034}, "5036": {"id": 5036, "desc": "娑堣€楅噾甯�259000", "beyongd": 3, "type": 6, "value": [259000], "rewards": [[11, 10]], "pre": 5035}, "5037": {"id": 5037, "desc": "娑堣€楅噾甯�266000", "beyongd": 3, "type": 6, "value": [266000], "rewards": [[11, 10]], "pre": 5036}, "5038": {"id": 5038, "desc": "娑堣€楅噾甯�273000", "beyongd": 3, "type": 6, "value": [273000], "rewards": [[11, 10]], "pre": 5037}, "5039": {"id": 5039, "desc": "娑堣€楅噾甯�280000", "beyongd": 3, "type": 6, "value": [280000], "rewards": [[11, 10]], "pre": 5038}, "5040": {"id": 5040, "desc": "娑堣€楅噾甯�287000", "beyongd": 3, "type": 6, "value": [287000], "rewards": [[11, 10]], "pre": 5039}, "5041": {"id": 5041, "desc": "娑堣€楅噾甯�294000", "beyongd": 3, "type": 6, "value": [294000], "rewards": [[11, 10]], "pre": 5040}, "5042": {"id": 5042, "desc": "娑堣€楅噾甯�301000", "beyongd": 3, "type": 6, "value": [301000], "rewards": [[11, 10]], "pre": 5041}, "5043": {"id": 5043, "desc": "娑堣€楅噾甯�308000", "beyongd": 3, "type": 6, "value": [308000], "rewards": [[11, 10]], "pre": 5042}, "5044": {"id": 5044, "desc": "娑堣€楅噾甯�315000", "beyongd": 3, "type": 6, "value": [315000], "rewards": [[11, 10]], "pre": 5043}, "5045": {"id": 5045, "desc": "娑堣€楅噾甯�322000", "beyongd": 3, "type": 6, "value": [322000], "rewards": [[11, 10]], "pre": 5044}, "5046": {"id": 5046, "desc": "娑堣€楅噾甯�329000", "beyongd": 3, "type": 6, "value": [329000], "rewards": [[11, 10]], "pre": 5045}, "5047": {"id": 5047, "desc": "娑堣€楅噾甯�336000", "beyongd": 3, "type": 6, "value": [336000], "rewards": [[11, 10]], "pre": 5046}, "5048": {"id": 5048, "desc": "娑堣€楅噾甯�343000", "beyongd": 3, "type": 6, "value": [343000], "rewards": [[11, 10]], "pre": 5047}, "5049": {"id": 5049, "desc": "娑堣€楅噾甯�350000", "beyongd": 3, "type": 6, "value": [350000], "rewards": [[11, 10]], "pre": 5048}, "5050": {"id": 5050, "desc": "娑堣€楅噾甯�357000", "beyongd": 3, "type": 6, "value": [357000], "rewards": [[11, 10]], "pre": 5049}, "5051": {"id": 5051, "desc": "娑堣€楅噾甯�364000", "beyongd": 3, "type": 6, "value": [364000], "rewards": [[11, 10]], "pre": 5050}, "5052": {"id": 5052, "desc": "娑堣€楅噾甯�371000", "beyongd": 3, "type": 6, "value": [371000], "rewards": [[11, 10]], "pre": 5051}, "5053": {"id": 5053, "desc": "娑堣€楅噾甯�378000", "beyongd": 3, "type": 6, "value": [378000], "rewards": [[11, 10]], "pre": 5052}, "5054": {"id": 5054, "desc": "娑堣€楅噾甯�385000", "beyongd": 3, "type": 6, "value": [385000], "rewards": [[11, 10]], "pre": 5053}, "5055": {"id": 5055, "desc": "娑堣€楅噾甯�392000", "beyongd": 3, "type": 6, "value": [392000], "rewards": [[11, 10]], "pre": 5054}, "5056": {"id": 5056, "desc": "娑堣€楅噾甯�399000", "beyongd": 3, "type": 6, "value": [399000], "rewards": [[11, 10]], "pre": 5055}, "5057": {"id": 5057, "desc": "娑堣€楅噾甯�406000", "beyongd": 3, "type": 6, "value": [406000], "rewards": [[11, 10]], "pre": 5056}, "5058": {"id": 5058, "desc": "娑堣€楅噾甯�413000", "beyongd": 3, "type": 6, "value": [413000], "rewards": [[11, 10]], "pre": 5057}, "5059": {"id": 5059, "desc": "娑堣€楅噾甯�420000", "beyongd": 3, "type": 6, "value": [420000], "rewards": [[11, 10]], "pre": 5058}, "5060": {"id": 5060, "desc": "娑堣€楅噾甯�427000", "beyongd": 3, "type": 6, "value": [427000], "rewards": [[11, 10]], "pre": 5059}, "5061": {"id": 5061, "desc": "娑堣€楅噾甯�434000", "beyongd": 3, "type": 6, "value": [434000], "rewards": [[11, 10]], "pre": 5060}, "5062": {"id": 5062, "desc": "娑堣€楅噾甯�10000", "beyongd": 3, "type": 6, "value": [10000], "rewards": [[11, 10]]}, "5063": {"id": 5063, "desc": "娑堣€楅噾甯�20000", "beyongd": 3, "type": 6, "value": [20000], "rewards": [[11, 10]], "pre": 5062}, "5064": {"id": 5064, "desc": "娑堣€楅噾甯�30000", "beyongd": 3, "type": 6, "value": [30000], "rewards": [[11, 10]], "pre": 5063}, "5065": {"id": 5065, "desc": "娑堣€楅噾甯�40000", "beyongd": 3, "type": 6, "value": [40000], "rewards": [[11, 10]], "pre": 5064}, "5066": {"id": 5066, "desc": "娑堣€楅噾甯�50000", "beyongd": 3, "type": 6, "value": [50000], "rewards": [[11, 10]], "pre": 5065}, "5067": {"id": 5067, "desc": "娑堣€楅噾甯�60000", "beyongd": 3, "type": 6, "value": [60000], "rewards": [[11, 10]], "pre": 5066}, "5068": {"id": 5068, "desc": "娑堣€楅噾甯�70000", "beyongd": 3, "type": 6, "value": [70000], "rewards": [[11, 10]], "pre": 5067}, "5069": {"id": 5069, "desc": "娑堣€楅噾甯�80000", "beyongd": 3, "type": 6, "value": [80000], "rewards": [[11, 10]], "pre": 5068}, "5070": {"id": 5070, "desc": "娑堣€楅噾甯�90000", "beyongd": 3, "type": 6, "value": [90000], "rewards": [[11, 10]], "pre": 5069}, "5071": {"id": 5071, "desc": "娑堣€楅噾甯�100000", "beyongd": 3, "type": 6, "value": [100000], "rewards": [[11, 10]], "pre": 5070}, "5072": {"id": 5072, "desc": "娑堣€楅噾甯�110000", "beyongd": 3, "type": 6, "value": [110000], "rewards": [[11, 10]], "pre": 5071}, "5073": {"id": 5073, "desc": "娑堣€楅噾甯�120000", "beyongd": 3, "type": 6, "value": [120000], "rewards": [[11, 10]], "pre": 5072}, "5074": {"id": 5074, "desc": "娑堣€楅噾甯�130000", "beyongd": 3, "type": 6, "value": [130000], "rewards": [[11, 10]], "pre": 5073}, "5075": {"id": 5075, "desc": "娑堣€楅噾甯�140000", "beyongd": 3, "type": 6, "value": [140000], "rewards": [[11, 10]], "pre": 5074}, "5076": {"id": 5076, "desc": "娑堣€楅噾甯�150000", "beyongd": 3, "type": 6, "value": [150000], "rewards": [[11, 10]], "pre": 5075}, "5077": {"id": 5077, "desc": "娑堣€楅噾甯�160000", "beyongd": 3, "type": 6, "value": [160000], "rewards": [[11, 10]], "pre": 5076}, "5078": {"id": 5078, "desc": "娑堣€楅噾甯�170000", "beyongd": 3, "type": 6, "value": [170000], "rewards": [[11, 10]], "pre": 5077}, "5079": {"id": 5079, "desc": "娑堣€楅噾甯�180000", "beyongd": 3, "type": 6, "value": [180000], "rewards": [[11, 10]], "pre": 5078}, "5080": {"id": 5080, "desc": "娑堣€楅噾甯�190000", "beyongd": 3, "type": 6, "value": [190000], "rewards": [[11, 10]], "pre": 5079}, "5081": {"id": 5081, "desc": "娑堣€楅噾甯�200000", "beyongd": 3, "type": 6, "value": [200000], "rewards": [[11, 10]], "pre": 5080}, "5082": {"id": 5082, "desc": "娑堣€楅噾甯�210000", "beyongd": 3, "type": 6, "value": [210000], "rewards": [[11, 10]], "pre": 5081}, "5083": {"id": 5083, "desc": "娑堣€楅噾甯�220000", "beyongd": 3, "type": 6, "value": [220000], "rewards": [[11, 10]], "pre": 5082}, "5084": {"id": 5084, "desc": "娑堣€楅噾甯�230000", "beyongd": 3, "type": 6, "value": [230000], "rewards": [[11, 10]], "pre": 5083}, "5085": {"id": 5085, "desc": "娑堣€楅噾甯�240000", "beyongd": 3, "type": 6, "value": [240000], "rewards": [[11, 10]], "pre": 5084}, "5086": {"id": 5086, "desc": "娑堣€楅噾甯�250000", "beyongd": 3, "type": 6, "value": [250000], "rewards": [[11, 10]], "pre": 5085}, "5087": {"id": 5087, "desc": "娑堣€楅噾甯�260000", "beyongd": 3, "type": 6, "value": [260000], "rewards": [[11, 10]], "pre": 5086}, "5088": {"id": 5088, "desc": "娑堣€楅噾甯�270000", "beyongd": 3, "type": 6, "value": [270000], "rewards": [[11, 10]], "pre": 5087}, "5089": {"id": 5089, "desc": "娑堣€楅噾甯�280000", "beyongd": 3, "type": 6, "value": [280000], "rewards": [[11, 10]], "pre": 5088}, "5090": {"id": 5090, "desc": "娑堣€楅噾甯�290000", "beyongd": 3, "type": 6, "value": [290000], "rewards": [[11, 10]], "pre": 5089}, "5091": {"id": 5091, "desc": "娑堣€楅噾甯�300000", "beyongd": 3, "type": 6, "value": [300000], "rewards": [[11, 10]], "pre": 5090}, "5092": {"id": 5092, "desc": "娑堣€楅噾甯�310000", "beyongd": 3, "type": 6, "value": [310000], "rewards": [[11, 10]], "pre": 5091}, "5093": {"id": 5093, "desc": "娑堣€楅噾甯�320000", "beyongd": 3, "type": 6, "value": [320000], "rewards": [[11, 10]], "pre": 5092}, "5094": {"id": 5094, "desc": "娑堣€楅噾甯�330000", "beyongd": 3, "type": 6, "value": [330000], "rewards": [[11, 10]], "pre": 5093}, "5095": {"id": 5095, "desc": "娑堣€楅噾甯�340000", "beyongd": 3, "type": 6, "value": [340000], "rewards": [[11, 10]], "pre": 5094}, "5096": {"id": 5096, "desc": "娑堣€楅噾甯�350000", "beyongd": 3, "type": 6, "value": [350000], "rewards": [[11, 10]], "pre": 5095}, "5097": {"id": 5097, "desc": "娑堣€楅噾甯�360000", "beyongd": 3, "type": 6, "value": [360000], "rewards": [[11, 10]], "pre": 5096}, "5098": {"id": 5098, "desc": "娑堣€楅噾甯�370000", "beyongd": 3, "type": 6, "value": [370000], "rewards": [[11, 10]], "pre": 5097}, "5099": {"id": 5099, "desc": "娑堣€楅噾甯�380000", "beyongd": 3, "type": 6, "value": [380000], "rewards": [[11, 10]], "pre": 5098}, "5100": {"id": 5100, "desc": "娑堣€楅噾甯�390000", "beyongd": 3, "type": 6, "value": [390000], "rewards": [[11, 10]], "pre": 5099}, "5101": {"id": 5101, "desc": "娑堣€楅噾甯�400000", "beyongd": 3, "type": 6, "value": [400000], "rewards": [[11, 10]], "pre": 5100}, "5102": {"id": 5102, "desc": "娑堣€楅噾甯�410000", "beyongd": 3, "type": 6, "value": [410000], "rewards": [[11, 10]], "pre": 5101}, "5103": {"id": 5103, "desc": "娑堣€楅噾甯�420000", "beyongd": 3, "type": 6, "value": [420000], "rewards": [[11, 10]], "pre": 5102}, "5104": {"id": 5104, "desc": "娑堣€楅噾甯�430000", "beyongd": 3, "type": 6, "value": [430000], "rewards": [[11, 10]], "pre": 5103}, "5105": {"id": 5105, "desc": "娑堣€楅噾甯�440000", "beyongd": 3, "type": 6, "value": [440000], "rewards": [[11, 10]], "pre": 5104}, "5106": {"id": 5106, "desc": "娑堣€楅噾甯�450000", "beyongd": 3, "type": 6, "value": [450000], "rewards": [[11, 10]], "pre": 5105}, "5107": {"id": 5107, "desc": "娑堣€楅噾甯�460000", "beyongd": 3, "type": 6, "value": [460000], "rewards": [[11, 10]], "pre": 5106}, "5108": {"id": 5108, "desc": "娑堣€楅噾甯�470000", "beyongd": 3, "type": 6, "value": [470000], "rewards": [[11, 10]], "pre": 5107}, "5109": {"id": 5109, "desc": "娑堣€楅噾甯�480000", "beyongd": 3, "type": 6, "value": [480000], "rewards": [[11, 10]], "pre": 5108}, "5110": {"id": 5110, "desc": "娑堣€楅噾甯�490000", "beyongd": 3, "type": 6, "value": [490000], "rewards": [[11, 10]], "pre": 5109}, "5111": {"id": 5111, "desc": "娑堣€楅噾甯�500000", "beyongd": 3, "type": 6, "value": [500000], "rewards": [[11, 10]], "pre": 5110}, "5112": {"id": 5112, "desc": "娑堣€楅噾甯�510000", "beyongd": 3, "type": 6, "value": [510000], "rewards": [[11, 10]], "pre": 5111}, "5113": {"id": 5113, "desc": "娑堣€楅噾甯�520000", "beyongd": 3, "type": 6, "value": [520000], "rewards": [[11, 10]], "pre": 5112}, "5114": {"id": 5114, "desc": "娑堣€楅噾甯�530000", "beyongd": 3, "type": 6, "value": [530000], "rewards": [[11, 10]], "pre": 5113}, "5115": {"id": 5115, "desc": "娑堣€楅噾甯�540000", "beyongd": 3, "type": 6, "value": [540000], "rewards": [[11, 10]], "pre": 5114}, "5116": {"id": 5116, "desc": "娑堣€楅噾甯�550000", "beyongd": 3, "type": 6, "value": [550000], "rewards": [[11, 10]], "pre": 5115}, "5117": {"id": 5117, "desc": "娑堣€楅噾甯�560000", "beyongd": 3, "type": 6, "value": [560000], "rewards": [[11, 10]], "pre": 5116}, "5118": {"id": 5118, "desc": "娑堣€楅噾甯�570000", "beyongd": 3, "type": 6, "value": [570000], "rewards": [[11, 10]], "pre": 5117}, "5119": {"id": 5119, "desc": "娑堣€楅噾甯�580000", "beyongd": 3, "type": 6, "value": [580000], "rewards": [[11, 10]], "pre": 5118}, "5120": {"id": 5120, "desc": "娑堣€楅噾甯�590000", "beyongd": 3, "type": 6, "value": [590000], "rewards": [[11, 10]], "pre": 5119}, "5121": {"id": 5121, "desc": "娑堣€楅噾甯�600000", "beyongd": 3, "type": 6, "value": [600000], "rewards": [[11, 10]], "pre": 5120}, "6000": {"id": 6000, "desc": "瀹濈寮€鍚�5娆�", "beyongd": 3, "type": 11, "value": [5], "rewards": [[11, 10]]}, "6001": {"id": 6001, "desc": "瀹濈寮€鍚�10娆�", "beyongd": 3, "type": 11, "value": [10], "rewards": [[11, 10]], "pre": 6000}, "6002": {"id": 6002, "desc": "瀹濈寮€鍚�15娆�", "beyongd": 3, "type": 11, "value": [15], "rewards": [[11, 10]], "pre": 6001}, "6003": {"id": 6003, "desc": "瀹濈寮€鍚�20娆�", "beyongd": 3, "type": 11, "value": [20], "rewards": [[11, 10]], "pre": 6002}, "6004": {"id": 6004, "desc": "瀹濈寮€鍚�25娆�", "beyongd": 3, "type": 11, "value": [25], "rewards": [[11, 10]], "pre": 6003}, "6005": {"id": 6005, "desc": "瀹濈寮€鍚�30娆�", "beyongd": 3, "type": 11, "value": [30], "rewards": [[11, 10]], "pre": 6004}, "6006": {"id": 6006, "desc": "瀹濈寮€鍚�35娆�", "beyongd": 3, "type": 11, "value": [35], "rewards": [[11, 10]], "pre": 6005}, "6007": {"id": 6007, "desc": "瀹濈寮€鍚�40娆�", "beyongd": 3, "type": 11, "value": [40], "rewards": [[11, 10]], "pre": 6006}, "6008": {"id": 6008, "desc": "瀹濈寮€鍚�45娆�", "beyongd": 3, "type": 11, "value": [45], "rewards": [[11, 10]], "pre": 6007}, "6009": {"id": 6009, "desc": "瀹濈寮€鍚�50娆�", "beyongd": 3, "type": 11, "value": [50], "rewards": [[11, 10]], "pre": 6008}, "6010": {"id": 6010, "desc": "瀹濈寮€鍚�55娆�", "beyongd": 3, "type": 11, "value": [55], "rewards": [[11, 10]], "pre": 6009}, "6011": {"id": 6011, "desc": "瀹濈寮€鍚�60娆�", "beyongd": 3, "type": 11, "value": [60], "rewards": [[11, 10]], "pre": 6010}, "6012": {"id": 6012, "desc": "瀹濈寮€鍚�65娆�", "beyongd": 3, "type": 11, "value": [65], "rewards": [[11, 10]], "pre": 6011}, "6013": {"id": 6013, "desc": "瀹濈寮€鍚�70娆�", "beyongd": 3, "type": 11, "value": [70], "rewards": [[11, 10]], "pre": 6012}, "6014": {"id": 6014, "desc": "瀹濈寮€鍚�75娆�", "beyongd": 3, "type": 11, "value": [75], "rewards": [[11, 10]], "pre": 6013}, "6015": {"id": 6015, "desc": "瀹濈寮€鍚�80娆�", "beyongd": 3, "type": 11, "value": [80], "rewards": [[11, 10]], "pre": 6014}, "6016": {"id": 6016, "desc": "瀹濈寮€鍚�85娆�", "beyongd": 3, "type": 11, "value": [85], "rewards": [[11, 10]], "pre": 6015}, "6017": {"id": 6017, "desc": "瀹濈寮€鍚�90娆�", "beyongd": 3, "type": 11, "value": [90], "rewards": [[11, 10]], "pre": 6016}, "6018": {"id": 6018, "desc": "瀹濈寮€鍚�95娆�", "beyongd": 3, "type": 11, "value": [95], "rewards": [[11, 10]], "pre": 6017}, "6019": {"id": 6019, "desc": "瀹濈寮€鍚�100娆�", "beyongd": 3, "type": 11, "value": [100], "rewards": [[11, 10]], "pre": 6018}, "6020": {"id": 6020, "desc": "瀹濈寮€鍚�105娆�", "beyongd": 3, "type": 11, "value": [105], "rewards": [[11, 10]], "pre": 6019}, "6021": {"id": 6021, "desc": "瀹濈寮€鍚�110娆�", "beyongd": 3, "type": 11, "value": [110], "rewards": [[11, 10]], "pre": 6020}, "6022": {"id": 6022, "desc": "瀹濈寮€鍚�115娆�", "beyongd": 3, "type": 11, "value": [115], "rewards": [[11, 10]], "pre": 6021}, "6023": {"id": 6023, "desc": "瀹濈寮€鍚�120娆�", "beyongd": 3, "type": 11, "value": [120], "rewards": [[11, 10]], "pre": 6022}, "6024": {"id": 6024, "desc": "瀹濈寮€鍚�125娆�", "beyongd": 3, "type": 11, "value": [125], "rewards": [[11, 10]], "pre": 6023}, "6025": {"id": 6025, "desc": "瀹濈寮€鍚�130娆�", "beyongd": 3, "type": 11, "value": [130], "rewards": [[11, 10]], "pre": 6024}, "6026": {"id": 6026, "desc": "瀹濈寮€鍚�135娆�", "beyongd": 3, "type": 11, "value": [135], "rewards": [[11, 10]], "pre": 6025}, "6027": {"id": 6027, "desc": "瀹濈寮€鍚�140娆�", "beyongd": 3, "type": 11, "value": [140], "rewards": [[11, 10]], "pre": 6026}, "6028": {"id": 6028, "desc": "瀹濈寮€鍚�145娆�", "beyongd": 3, "type": 11, "value": [145], "rewards": [[11, 10]], "pre": 6027}, "6029": {"id": 6029, "desc": "瀹濈寮€鍚�150娆�", "beyongd": 3, "type": 11, "value": [150], "rewards": [[11, 10]], "pre": 6028}, "6030": {"id": 6030, "desc": "瀹濈寮€鍚�155娆�", "beyongd": 3, "type": 11, "value": [155], "rewards": [[11, 10]], "pre": 6029}, "6031": {"id": 6031, "desc": "瀹濈寮€鍚�160娆�", "beyongd": 3, "type": 11, "value": [160], "rewards": [[11, 10]], "pre": 6030}, "6032": {"id": 6032, "desc": "瀹濈寮€鍚�165娆�", "beyongd": 3, "type": 11, "value": [165], "rewards": [[11, 10]], "pre": 6031}, "6033": {"id": 6033, "desc": "瀹濈寮€鍚�170娆�", "beyongd": 3, "type": 11, "value": [170], "rewards": [[11, 10]], "pre": 6032}, "6034": {"id": 6034, "desc": "瀹濈寮€鍚�175娆�", "beyongd": 3, "type": 11, "value": [175], "rewards": [[11, 10]], "pre": 6033}, "6035": {"id": 6035, "desc": "瀹濈寮€鍚�180娆�", "beyongd": 3, "type": 11, "value": [180], "rewards": [[11, 10]], "pre": 6034}, "6036": {"id": 6036, "desc": "瀹濈寮€鍚�185娆�", "beyongd": 3, "type": 11, "value": [185], "rewards": [[11, 10]], "pre": 6035}, "6037": {"id": 6037, "desc": "瀹濈寮€鍚�190娆�", "beyongd": 3, "type": 11, "value": [190], "rewards": [[11, 10]], "pre": 6036}, "6038": {"id": 6038, "desc": "瀹濈寮€鍚�195娆�", "beyongd": 3, "type": 11, "value": [195], "rewards": [[11, 10]], "pre": 6037}, "6039": {"id": 6039, "desc": "瀹濈寮€鍚�200娆�", "beyongd": 3, "type": 11, "value": [200], "rewards": [[11, 10]], "pre": 6038}, "6040": {"id": 6040, "desc": "瀹濈寮€鍚�205娆�", "beyongd": 3, "type": 11, "value": [205], "rewards": [[11, 10]], "pre": 6039}, "6041": {"id": 6041, "desc": "瀹濈寮€鍚�210娆�", "beyongd": 3, "type": 11, "value": [210], "rewards": [[11, 10]], "pre": 6040}, "6042": {"id": 6042, "desc": "瀹濈寮€鍚�215娆�", "beyongd": 3, "type": 11, "value": [215], "rewards": [[11, 10]], "pre": 6041}, "6043": {"id": 6043, "desc": "瀹濈寮€鍚�220娆�", "beyongd": 3, "type": 11, "value": [220], "rewards": [[11, 10]], "pre": 6042}, "6044": {"id": 6044, "desc": "瀹濈寮€鍚�225娆�", "beyongd": 3, "type": 11, "value": [225], "rewards": [[11, 10]], "pre": 6043}, "6045": {"id": 6045, "desc": "瀹濈寮€鍚�230娆�", "beyongd": 3, "type": 11, "value": [230], "rewards": [[11, 10]], "pre": 6044}, "6046": {"id": 6046, "desc": "瀹濈寮€鍚�235娆�", "beyongd": 3, "type": 11, "value": [235], "rewards": [[11, 10]], "pre": 6045}, "6047": {"id": 6047, "desc": "瀹濈寮€鍚�240娆�", "beyongd": 3, "type": 11, "value": [240], "rewards": [[11, 10]], "pre": 6046}, "6048": {"id": 6048, "desc": "瀹濈寮€鍚�245娆�", "beyongd": 3, "type": 11, "value": [245], "rewards": [[11, 10]], "pre": 6047}, "6049": {"id": 6049, "desc": "瀹濈寮€鍚�250娆�", "beyongd": 3, "type": 11, "value": [250], "rewards": [[11, 10]], "pre": 6048}, "6050": {"id": 6050, "desc": "瀹濈寮€鍚�255娆�", "beyongd": 3, "type": 11, "value": [255], "rewards": [[11, 10]], "pre": 6049}, "6051": {"id": 6051, "desc": "瀹濈寮€鍚�260娆�", "beyongd": 3, "type": 11, "value": [260], "rewards": [[11, 10]], "pre": 6050}, "6052": {"id": 6052, "desc": "瀹濈寮€鍚�265娆�", "beyongd": 3, "type": 11, "value": [265], "rewards": [[11, 10]], "pre": 6051}, "6053": {"id": 6053, "desc": "瀹濈寮€鍚�270娆�", "beyongd": 3, "type": 11, "value": [270], "rewards": [[11, 10]], "pre": 6052}, "6054": {"id": 6054, "desc": "瀹濈寮€鍚�275娆�", "beyongd": 3, "type": 11, "value": [275], "rewards": [[11, 10]], "pre": 6053}, "6055": {"id": 6055, "desc": "瀹濈寮€鍚�280娆�", "beyongd": 3, "type": 11, "value": [280], "rewards": [[11, 10]], "pre": 6054}, "6056": {"id": 6056, "desc": "瀹濈寮€鍚�285娆�", "beyongd": 3, "type": 11, "value": [285], "rewards": [[11, 10]], "pre": 6055}, "6057": {"id": 6057, "desc": "瀹濈寮€鍚�290娆�", "beyongd": 3, "type": 11, "value": [290], "rewards": [[11, 10]], "pre": 6056}, "6058": {"id": 6058, "desc": "瀹濈寮€鍚�295娆�", "beyongd": 3, "type": 11, "value": [295], "rewards": [[11, 10]], "pre": 6057}, "6059": {"id": 6059, "desc": "瀹濈寮€鍚�300娆�", "beyongd": 3, "type": 11, "value": [300], "rewards": [[11, 10]], "pre": 6058}, "6060": {"id": 6060, "desc": "瀹濈寮€鍚�10娆�", "beyongd": 3, "type": 11, "value": [10], "rewards": [[11, 10]]}, "6061": {"id": 6061, "desc": "瀹濈寮€鍚�20娆�", "beyongd": 3, "type": 11, "value": [20], "rewards": [[11, 10]], "pre": 6060}, "6062": {"id": 6062, "desc": "瀹濈寮€鍚�30娆�", "beyongd": 3, "type": 11, "value": [30], "rewards": [[11, 10]], "pre": 6061}, "6063": {"id": 6063, "desc": "瀹濈寮€鍚�40娆�", "beyongd": 3, "type": 11, "value": [40], "rewards": [[11, 10]], "pre": 6062}, "6064": {"id": 6064, "desc": "瀹濈寮€鍚�50娆�", "beyongd": 3, "type": 11, "value": [50], "rewards": [[11, 10]], "pre": 6063}, "6065": {"id": 6065, "desc": "瀹濈寮€鍚�60娆�", "beyongd": 3, "type": 11, "value": [60], "rewards": [[11, 10]], "pre": 6064}, "6066": {"id": 6066, "desc": "瀹濈寮€鍚�70娆�", "beyongd": 3, "type": 11, "value": [70], "rewards": [[11, 10]], "pre": 6065}, "6067": {"id": 6067, "desc": "瀹濈寮€鍚�80娆�", "beyongd": 3, "type": 11, "value": [80], "rewards": [[11, 10]], "pre": 6066}, "6068": {"id": 6068, "desc": "瀹濈寮€鍚�90娆�", "beyongd": 3, "type": 11, "value": [90], "rewards": [[11, 10]], "pre": 6067}, "6069": {"id": 6069, "desc": "瀹濈寮€鍚�100娆�", "beyongd": 3, "type": 11, "value": [100], "rewards": [[11, 10]], "pre": 6068}, "6070": {"id": 6070, "desc": "瀹濈寮€鍚�110娆�", "beyongd": 3, "type": 11, "value": [110], "rewards": [[11, 10]], "pre": 6069}, "6071": {"id": 6071, "desc": "瀹濈寮€鍚�120娆�", "beyongd": 3, "type": 11, "value": [120], "rewards": [[11, 10]], "pre": 6070}, "6072": {"id": 6072, "desc": "瀹濈寮€鍚�130娆�", "beyongd": 3, "type": 11, "value": [130], "rewards": [[11, 10]], "pre": 6071}, "6073": {"id": 6073, "desc": "瀹濈寮€鍚�140娆�", "beyongd": 3, "type": 11, "value": [140], "rewards": [[11, 10]], "pre": 6072}, "6074": {"id": 6074, "desc": "瀹濈寮€鍚�150娆�", "beyongd": 3, "type": 11, "value": [150], "rewards": [[11, 10]], "pre": 6073}, "6075": {"id": 6075, "desc": "瀹濈寮€鍚�160娆�", "beyongd": 3, "type": 11, "value": [160], "rewards": [[11, 10]], "pre": 6074}, "6076": {"id": 6076, "desc": "瀹濈寮€鍚�170娆�", "beyongd": 3, "type": 11, "value": [170], "rewards": [[11, 10]], "pre": 6075}, "6077": {"id": 6077, "desc": "瀹濈寮€鍚�180娆�", "beyongd": 3, "type": 11, "value": [180], "rewards": [[11, 10]], "pre": 6076}, "6078": {"id": 6078, "desc": "瀹濈寮€鍚�190娆�", "beyongd": 3, "type": 11, "value": [190], "rewards": [[11, 10]], "pre": 6077}, "6079": {"id": 6079, "desc": "瀹濈寮€鍚�200娆�", "beyongd": 3, "type": 11, "value": [200], "rewards": [[11, 10]], "pre": 6078}, "6080": {"id": 6080, "desc": "瀹濈寮€鍚�210娆�", "beyongd": 3, "type": 11, "value": [210], "rewards": [[11, 10]], "pre": 6079}, "6081": {"id": 6081, "desc": "瀹濈寮€鍚�220娆�", "beyongd": 3, "type": 11, "value": [220], "rewards": [[11, 10]], "pre": 6080}, "6082": {"id": 6082, "desc": "瀹濈寮€鍚�230娆�", "beyongd": 3, "type": 11, "value": [230], "rewards": [[11, 10]], "pre": 6081}, "6083": {"id": 6083, "desc": "瀹濈寮€鍚�240娆�", "beyongd": 3, "type": 11, "value": [240], "rewards": [[11, 10]], "pre": 6082}, "6084": {"id": 6084, "desc": "瀹濈寮€鍚�250娆�", "beyongd": 3, "type": 11, "value": [250], "rewards": [[11, 10]], "pre": 6083}, "6085": {"id": 6085, "desc": "瀹濈寮€鍚�260娆�", "beyongd": 3, "type": 11, "value": [260], "rewards": [[11, 10]], "pre": 6084}, "6086": {"id": 6086, "desc": "瀹濈寮€鍚�270娆�", "beyongd": 3, "type": 11, "value": [270], "rewards": [[11, 10]], "pre": 6085}, "6087": {"id": 6087, "desc": "瀹濈寮€鍚�280娆�", "beyongd": 3, "type": 11, "value": [280], "rewards": [[11, 10]], "pre": 6086}, "6088": {"id": 6088, "desc": "瀹濈寮€鍚�290娆�", "beyongd": 3, "type": 11, "value": [290], "rewards": [[11, 10]], "pre": 6087}, "6089": {"id": 6089, "desc": "瀹濈寮€鍚�300娆�", "beyongd": 3, "type": 11, "value": [300], "rewards": [[11, 10]], "pre": 6088}, "6090": {"id": 6090, "desc": "瀹濈寮€鍚�310娆�", "beyongd": 3, "type": 11, "value": [310], "rewards": [[11, 10]], "pre": 6089}, "6091": {"id": 6091, "desc": "瀹濈寮€鍚�320娆�", "beyongd": 3, "type": 11, "value": [320], "rewards": [[11, 10]], "pre": 6090}, "6092": {"id": 6092, "desc": "瀹濈寮€鍚�330娆�", "beyongd": 3, "type": 11, "value": [330], "rewards": [[11, 10]], "pre": 6091}, "6093": {"id": 6093, "desc": "瀹濈寮€鍚�340娆�", "beyongd": 3, "type": 11, "value": [340], "rewards": [[11, 10]], "pre": 6092}, "6094": {"id": 6094, "desc": "瀹濈寮€鍚�350娆�", "beyongd": 3, "type": 11, "value": [350], "rewards": [[11, 10]], "pre": 6093}, "6095": {"id": 6095, "desc": "瀹濈寮€鍚�360娆�", "beyongd": 3, "type": 11, "value": [360], "rewards": [[11, 10]], "pre": 6094}, "6096": {"id": 6096, "desc": "瀹濈寮€鍚�370娆�", "beyongd": 3, "type": 11, "value": [370], "rewards": [[11, 10]], "pre": 6095}, "6097": {"id": 6097, "desc": "瀹濈寮€鍚�380娆�", "beyongd": 3, "type": 11, "value": [380], "rewards": [[11, 10]], "pre": 6096}, "6098": {"id": 6098, "desc": "瀹濈寮€鍚�390娆�", "beyongd": 3, "type": 11, "value": [390], "rewards": [[11, 10]], "pre": 6097}, "6099": {"id": 6099, "desc": "瀹濈寮€鍚�400娆�", "beyongd": 3, "type": 11, "value": [400], "rewards": [[11, 10]], "pre": 6098}, "6100": {"id": 6100, "desc": "瀹濈寮€鍚�410娆�", "beyongd": 3, "type": 11, "value": [410], "rewards": [[11, 10]], "pre": 6099}, "6101": {"id": 6101, "desc": "瀹濈寮€鍚�420娆�", "beyongd": 3, "type": 11, "value": [420], "rewards": [[11, 10]], "pre": 6100}, "6102": {"id": 6102, "desc": "瀹濈寮€鍚�430娆�", "beyongd": 3, "type": 11, "value": [430], "rewards": [[11, 10]], "pre": 6101}, "6103": {"id": 6103, "desc": "瀹濈寮€鍚�440娆�", "beyongd": 3, "type": 11, "value": [440], "rewards": [[11, 10]], "pre": 6102}, "6104": {"id": 6104, "desc": "瀹濈寮€鍚�450娆�", "beyongd": 3, "type": 11, "value": [450], "rewards": [[11, 10]], "pre": 6103}, "6105": {"id": 6105, "desc": "瀹濈寮€鍚�460娆�", "beyongd": 3, "type": 11, "value": [460], "rewards": [[11, 10]], "pre": 6104}, "6106": {"id": 6106, "desc": "瀹濈寮€鍚�470娆�", "beyongd": 3, "type": 11, "value": [470], "rewards": [[11, 10]], "pre": 6105}, "6107": {"id": 6107, "desc": "瀹濈寮€鍚�480娆�", "beyongd": 3, "type": 11, "value": [480], "rewards": [[11, 10]], "pre": 6106}, "6108": {"id": 6108, "desc": "瀹濈寮€鍚�490娆�", "beyongd": 3, "type": 11, "value": [490], "rewards": [[11, 10]], "pre": 6107}, "6109": {"id": 6109, "desc": "瀹濈寮€鍚�500娆�", "beyongd": 3, "type": 11, "value": [500], "rewards": [[11, 10]], "pre": 6108}, "6110": {"id": 6110, "desc": "瀹濈寮€鍚�510娆�", "beyongd": 3, "type": 11, "value": [510], "rewards": [[11, 10]], "pre": 6109}, "6111": {"id": 6111, "desc": "瀹濈寮€鍚�520娆�", "beyongd": 3, "type": 11, "value": [520], "rewards": [[11, 10]], "pre": 6110}, "6112": {"id": 6112, "desc": "瀹濈寮€鍚�530娆�", "beyongd": 3, "type": 11, "value": [530], "rewards": [[11, 10]], "pre": 6111}, "6113": {"id": 6113, "desc": "瀹濈寮€鍚�540娆�", "beyongd": 3, "type": 11, "value": [540], "rewards": [[11, 10]], "pre": 6112}, "6114": {"id": 6114, "desc": "瀹濈寮€鍚�550娆�", "beyongd": 3, "type": 11, "value": [550], "rewards": [[11, 10]], "pre": 6113}, "6115": {"id": 6115, "desc": "瀹濈寮€鍚�560娆�", "beyongd": 3, "type": 11, "value": [560], "rewards": [[11, 10]], "pre": 6114}, "6116": {"id": 6116, "desc": "瀹濈寮€鍚�570娆�", "beyongd": 3, "type": 11, "value": [570], "rewards": [[11, 10]], "pre": 6115}, "6117": {"id": 6117, "desc": "瀹濈寮€鍚�580娆�", "beyongd": 3, "type": 11, "value": [580], "rewards": [[11, 10]], "pre": 6116}, "6118": {"id": 6118, "desc": "瀹濈寮€鍚�590娆�", "beyongd": 3, "type": 11, "value": [590], "rewards": [[11, 10]], "pre": 6117}, "6119": {"id": 6119, "desc": "瀹濈寮€鍚�600娆�", "beyongd": 3, "type": 11, "value": [600], "rewards": [[11, 10]], "pre": 6118}, "7000": {"id": 7000, "desc": "鍑绘潃1000鍙€墿", "beyongd": 3, "type": 12, "value": [1000], "rewards": [[11, 10]]}, "7001": {"id": 7001, "desc": "鍑绘潃2000鍙€墿", "beyongd": 3, "type": 12, "value": [2000], "rewards": [[11, 10]], "pre": 7000}, "7002": {"id": 7002, "desc": "鍑绘潃3000鍙€墿", "beyongd": 3, "type": 12, "value": [3000], "rewards": [[11, 10]], "pre": 7001}, "7003": {"id": 7003, "desc": "鍑绘潃4000鍙€墿", "beyongd": 3, "type": 12, "value": [4000], "rewards": [[11, 10]], "pre": 7002}, "7004": {"id": 7004, "desc": "鍑绘潃5000鍙€墿", "beyongd": 3, "type": 12, "value": [5000], "rewards": [[11, 10]], "pre": 7003}, "7005": {"id": 7005, "desc": "鍑绘潃6000鍙€墿", "beyongd": 3, "type": 12, "value": [6000], "rewards": [[11, 10]], "pre": 7004}, "7006": {"id": 7006, "desc": "鍑绘潃7000鍙€墿", "beyongd": 3, "type": 12, "value": [7000], "rewards": [[11, 10]], "pre": 7005}, "7007": {"id": 7007, "desc": "鍑绘潃8000鍙€墿", "beyongd": 3, "type": 12, "value": [8000], "rewards": [[11, 10]], "pre": 7006}, "7008": {"id": 7008, "desc": "鍑绘潃9000鍙€墿", "beyongd": 3, "type": 12, "value": [9000], "rewards": [[11, 10]], "pre": 7007}, "7009": {"id": 7009, "desc": "鍑绘潃10000鍙€墿", "beyongd": 3, "type": 12, "value": [10000], "rewards": [[11, 10]], "pre": 7008}, "7010": {"id": 7010, "desc": "鍑绘潃11000鍙€墿", "beyongd": 3, "type": 12, "value": [11000], "rewards": [[11, 10]], "pre": 7009}, "7011": {"id": 7011, "desc": "鍑绘潃12000鍙€墿", "beyongd": 3, "type": 12, "value": [12000], "rewards": [[11, 10]], "pre": 7010}, "7012": {"id": 7012, "desc": "鍑绘潃13000鍙€墿", "beyongd": 3, "type": 12, "value": [13000], "rewards": [[11, 10]], "pre": 7011}, "7013": {"id": 7013, "desc": "鍑绘潃14000鍙€墿", "beyongd": 3, "type": 12, "value": [14000], "rewards": [[11, 10]], "pre": 7012}, "7014": {"id": 7014, "desc": "鍑绘潃15000鍙€墿", "beyongd": 3, "type": 12, "value": [15000], "rewards": [[11, 10]], "pre": 7013}, "7015": {"id": 7015, "desc": "鍑绘潃16000鍙€墿", "beyongd": 3, "type": 12, "value": [16000], "rewards": [[11, 10]], "pre": 7014}, "7016": {"id": 7016, "desc": "鍑绘潃17000鍙€墿", "beyongd": 3, "type": 12, "value": [17000], "rewards": [[11, 10]], "pre": 7015}, "7017": {"id": 7017, "desc": "鍑绘潃18000鍙€墿", "beyongd": 3, "type": 12, "value": [18000], "rewards": [[11, 10]], "pre": 7016}, "7018": {"id": 7018, "desc": "鍑绘潃19000鍙€墿", "beyongd": 3, "type": 12, "value": [19000], "rewards": [[11, 10]], "pre": 7017}, "7019": {"id": 7019, "desc": "鍑绘潃20000鍙€墿", "beyongd": 3, "type": 12, "value": [20000], "rewards": [[11, 10]], "pre": 7018}, "7020": {"id": 7020, "desc": "鍑绘潃21000鍙€墿", "beyongd": 3, "type": 12, "value": [21000], "rewards": [[11, 10]], "pre": 7019}, "7021": {"id": 7021, "desc": "鍑绘潃22000鍙€墿", "beyongd": 3, "type": 12, "value": [22000], "rewards": [[11, 10]], "pre": 7020}, "7022": {"id": 7022, "desc": "鍑绘潃23000鍙€墿", "beyongd": 3, "type": 12, "value": [23000], "rewards": [[11, 10]], "pre": 7021}, "7023": {"id": 7023, "desc": "鍑绘潃24000鍙€墿", "beyongd": 3, "type": 12, "value": [24000], "rewards": [[11, 10]], "pre": 7022}, "7024": {"id": 7024, "desc": "鍑绘潃25000鍙€墿", "beyongd": 3, "type": 12, "value": [25000], "rewards": [[11, 10]], "pre": 7023}, "7025": {"id": 7025, "desc": "鍑绘潃26000鍙€墿", "beyongd": 3, "type": 12, "value": [26000], "rewards": [[11, 10]], "pre": 7024}, "7026": {"id": 7026, "desc": "鍑绘潃27000鍙€墿", "beyongd": 3, "type": 12, "value": [27000], "rewards": [[11, 10]], "pre": 7025}, "7027": {"id": 7027, "desc": "鍑绘潃28000鍙€墿", "beyongd": 3, "type": 12, "value": [28000], "rewards": [[11, 10]], "pre": 7026}, "7028": {"id": 7028, "desc": "鍑绘潃29000鍙€墿", "beyongd": 3, "type": 12, "value": [29000], "rewards": [[11, 10]], "pre": 7027}, "7029": {"id": 7029, "desc": "鍑绘潃30000鍙€墿", "beyongd": 3, "type": 12, "value": [30000], "rewards": [[11, 10]], "pre": 7028}, "7030": {"id": 7030, "desc": "鍑绘潃31000鍙€墿", "beyongd": 3, "type": 12, "value": [31000], "rewards": [[11, 10]], "pre": 7029}, "7031": {"id": 7031, "desc": "鍑绘潃32000鍙€墿", "beyongd": 3, "type": 12, "value": [32000], "rewards": [[11, 10]], "pre": 7030}, "7032": {"id": 7032, "desc": "鍑绘潃33000鍙€墿", "beyongd": 3, "type": 12, "value": [33000], "rewards": [[11, 10]], "pre": 7031}, "7033": {"id": 7033, "desc": "鍑绘潃34000鍙€墿", "beyongd": 3, "type": 12, "value": [34000], "rewards": [[11, 10]], "pre": 7032}, "7034": {"id": 7034, "desc": "鍑绘潃35000鍙€墿", "beyongd": 3, "type": 12, "value": [35000], "rewards": [[11, 10]], "pre": 7033}, "7035": {"id": 7035, "desc": "鍑绘潃36000鍙€墿", "beyongd": 3, "type": 12, "value": [36000], "rewards": [[11, 10]], "pre": 7034}, "7036": {"id": 7036, "desc": "鍑绘潃37000鍙€墿", "beyongd": 3, "type": 12, "value": [37000], "rewards": [[11, 10]], "pre": 7035}, "7037": {"id": 7037, "desc": "鍑绘潃38000鍙€墿", "beyongd": 3, "type": 12, "value": [38000], "rewards": [[11, 10]], "pre": 7036}, "7038": {"id": 7038, "desc": "鍑绘潃39000鍙€墿", "beyongd": 3, "type": 12, "value": [39000], "rewards": [[11, 10]], "pre": 7037}, "7039": {"id": 7039, "desc": "鍑绘潃40000鍙€墿", "beyongd": 3, "type": 12, "value": [40000], "rewards": [[11, 10]], "pre": 7038}, "7040": {"id": 7040, "desc": "鍑绘潃41000鍙€墿", "beyongd": 3, "type": 12, "value": [41000], "rewards": [[11, 10]], "pre": 7039}, "7041": {"id": 7041, "desc": "鍑绘潃42000鍙€墿", "beyongd": 3, "type": 12, "value": [42000], "rewards": [[11, 10]], "pre": 7040}, "7042": {"id": 7042, "desc": "鍑绘潃43000鍙€墿", "beyongd": 3, "type": 12, "value": [43000], "rewards": [[11, 10]], "pre": 7041}, "7043": {"id": 7043, "desc": "鍑绘潃44000鍙€墿", "beyongd": 3, "type": 12, "value": [44000], "rewards": [[11, 10]], "pre": 7042}, "7044": {"id": 7044, "desc": "鍑绘潃45000鍙€墿", "beyongd": 3, "type": 12, "value": [45000], "rewards": [[11, 10]], "pre": 7043}, "7045": {"id": 7045, "desc": "鍑绘潃46000鍙€墿", "beyongd": 3, "type": 12, "value": [46000], "rewards": [[11, 10]], "pre": 7044}, "7046": {"id": 7046, "desc": "鍑绘潃47000鍙€墿", "beyongd": 3, "type": 12, "value": [47000], "rewards": [[11, 10]], "pre": 7045}, "7047": {"id": 7047, "desc": "鍑绘潃48000鍙€墿", "beyongd": 3, "type": 12, "value": [48000], "rewards": [[11, 10]], "pre": 7046}, "7048": {"id": 7048, "desc": "鍑绘潃49000鍙€墿", "beyongd": 3, "type": 12, "value": [49000], "rewards": [[11, 10]], "pre": 7047}, "7049": {"id": 7049, "desc": "鍑绘潃50000鍙€墿", "beyongd": 3, "type": 12, "value": [50000], "rewards": [[11, 10]], "pre": 7048}, "7050": {"id": 7050, "desc": "鍑绘潃51000鍙€墿", "beyongd": 3, "type": 12, "value": [51000], "rewards": [[11, 10]], "pre": 7049}, "7051": {"id": 7051, "desc": "鍑绘潃52000鍙€墿", "beyongd": 3, "type": 12, "value": [52000], "rewards": [[11, 10]], "pre": 7050}, "7052": {"id": 7052, "desc": "鍑绘潃53000鍙€墿", "beyongd": 3, "type": 12, "value": [53000], "rewards": [[11, 10]], "pre": 7051}, "7053": {"id": 7053, "desc": "鍑绘潃54000鍙€墿", "beyongd": 3, "type": 12, "value": [54000], "rewards": [[11, 10]], "pre": 7052}, "7054": {"id": 7054, "desc": "鍑绘潃55000鍙€墿", "beyongd": 3, "type": 12, "value": [55000], "rewards": [[11, 10]], "pre": 7053}, "7055": {"id": 7055, "desc": "鍑绘潃56000鍙€墿", "beyongd": 3, "type": 12, "value": [56000], "rewards": [[11, 10]], "pre": 7054}, "7056": {"id": 7056, "desc": "鍑绘潃57000鍙€墿", "beyongd": 3, "type": 12, "value": [57000], "rewards": [[11, 10]], "pre": 7055}, "7057": {"id": 7057, "desc": "鍑绘潃58000鍙€墿", "beyongd": 3, "type": 12, "value": [58000], "rewards": [[11, 10]], "pre": 7056}, "7058": {"id": 7058, "desc": "鍑绘潃59000鍙€墿", "beyongd": 3, "type": 12, "value": [59000], "rewards": [[11, 10]], "pre": 7057}, "7059": {"id": 7059, "desc": "鍑绘潃60000鍙€墿", "beyongd": 3, "type": 12, "value": [60000], "rewards": [[11, 10]], "pre": 7058}, "8000": {"id": 8000, "desc": "鍑绘潃1鍙狟OSS", "beyongd": 3, "type": 13, "value": [1], "rewards": [[11, 10]]}, "8001": {"id": 8001, "desc": "鍑绘潃2鍙狟OSS", "beyongd": 3, "type": 13, "value": [2], "rewards": [[11, 10]], "pre": 8000}, "8002": {"id": 8002, "desc": "鍑绘潃3鍙狟OSS", "beyongd": 3, "type": 13, "value": [3], "rewards": [[11, 10]], "pre": 8001}, "8003": {"id": 8003, "desc": "鍑绘潃4鍙狟OSS", "beyongd": 3, "type": 13, "value": [4], "rewards": [[11, 10]], "pre": 8002}, "8004": {"id": 8004, "desc": "鍑绘潃5鍙狟OSS", "beyongd": 3, "type": 13, "value": [5], "rewards": [[11, 10]], "pre": 8003}, "8005": {"id": 8005, "desc": "鍑绘潃6鍙狟OSS", "beyongd": 3, "type": 13, "value": [6], "rewards": [[11, 10]], "pre": 8004}, "8006": {"id": 8006, "desc": "鍑绘潃7鍙狟OSS", "beyongd": 3, "type": 13, "value": [7], "rewards": [[11, 10]], "pre": 8005}, "8007": {"id": 8007, "desc": "鍑绘潃8鍙狟OSS", "beyongd": 3, "type": 13, "value": [8], "rewards": [[11, 10]], "pre": 8006}, "8008": {"id": 8008, "desc": "鍑绘潃9鍙狟OSS", "beyongd": 3, "type": 13, "value": [9], "rewards": [[11, 10]], "pre": 8007}, "8009": {"id": 8009, "desc": "鍑绘潃10鍙狟OSS", "beyongd": 3, "type": 13, "value": [10], "rewards": [[11, 10]], "pre": 8008}, "8010": {"id": 8010, "desc": "鍑绘潃11鍙狟OSS", "beyongd": 3, "type": 13, "value": [11], "rewards": [[11, 10]], "pre": 8009}, "8011": {"id": 8011, "desc": "鍑绘潃12鍙狟OSS", "beyongd": 3, "type": 13, "value": [12], "rewards": [[11, 10]], "pre": 8010}, "8012": {"id": 8012, "desc": "鍑绘潃13鍙狟OSS", "beyongd": 3, "type": 13, "value": [13], "rewards": [[11, 10]], "pre": 8011}, "8013": {"id": 8013, "desc": "鍑绘潃14鍙狟OSS", "beyongd": 3, "type": 13, "value": [14], "rewards": [[11, 10]], "pre": 8012}, "8014": {"id": 8014, "desc": "鍑绘潃15鍙狟OSS", "beyongd": 3, "type": 13, "value": [15], "rewards": [[11, 10]], "pre": 8013}, "8015": {"id": 8015, "desc": "鍑绘潃16鍙狟OSS", "beyongd": 3, "type": 13, "value": [16], "rewards": [[11, 10]], "pre": 8014}, "8016": {"id": 8016, "desc": "鍑绘潃17鍙狟OSS", "beyongd": 3, "type": 13, "value": [17], "rewards": [[11, 10]], "pre": 8015}, "8017": {"id": 8017, "desc": "鍑绘潃18鍙狟OSS", "beyongd": 3, "type": 13, "value": [18], "rewards": [[11, 10]], "pre": 8016}, "8018": {"id": 8018, "desc": "鍑绘潃19鍙狟OSS", "beyongd": 3, "type": 13, "value": [19], "rewards": [[11, 10]], "pre": 8017}, "8019": {"id": 8019, "desc": "鍑绘潃20鍙狟OSS", "beyongd": 3, "type": 13, "value": [20], "rewards": [[11, 10]], "pre": 8018}, "8020": {"id": 8020, "desc": "鍑绘潃21鍙狟OSS", "beyongd": 3, "type": 13, "value": [21], "rewards": [[11, 10]], "pre": 8019}, "8021": {"id": 8021, "desc": "鍑绘潃22鍙狟OSS", "beyongd": 3, "type": 13, "value": [22], "rewards": [[11, 10]], "pre": 8020}, "8022": {"id": 8022, "desc": "鍑绘潃23鍙狟OSS", "beyongd": 3, "type": 13, "value": [23], "rewards": [[11, 10]], "pre": 8021}, "8023": {"id": 8023, "desc": "鍑绘潃24鍙狟OSS", "beyongd": 3, "type": 13, "value": [24], "rewards": [[11, 10]], "pre": 8022}, "8024": {"id": 8024, "desc": "鍑绘潃25鍙狟OSS", "beyongd": 3, "type": 13, "value": [25], "rewards": [[11, 10]], "pre": 8023}, "8025": {"id": 8025, "desc": "鍑绘潃26鍙狟OSS", "beyongd": 3, "type": 13, "value": [26], "rewards": [[11, 10]], "pre": 8024}, "8026": {"id": 8026, "desc": "鍑绘潃27鍙狟OSS", "beyongd": 3, "type": 13, "value": [27], "rewards": [[11, 10]], "pre": 8025}, "8027": {"id": 8027, "desc": "鍑绘潃28鍙狟OSS", "beyongd": 3, "type": 13, "value": [28], "rewards": [[11, 10]], "pre": 8026}, "8028": {"id": 8028, "desc": "鍑绘潃29鍙狟OSS", "beyongd": 3, "type": 13, "value": [29], "rewards": [[11, 10]], "pre": 8027}, "8029": {"id": 8029, "desc": "鍑绘潃30鍙狟OSS", "beyongd": 3, "type": 13, "value": [30], "rewards": [[11, 10]], "pre": 8028}, "8030": {"id": 8030, "desc": "鍑绘潃31鍙狟OSS", "beyongd": 3, "type": 13, "value": [31], "rewards": [[11, 10]], "pre": 8029}, "8031": {"id": 8031, "desc": "鍑绘潃32鍙狟OSS", "beyongd": 3, "type": 13, "value": [32], "rewards": [[11, 10]], "pre": 8030}, "8032": {"id": 8032, "desc": "鍑绘潃33鍙狟OSS", "beyongd": 3, "type": 13, "value": [33], "rewards": [[11, 10]], "pre": 8031}, "8033": {"id": 8033, "desc": "鍑绘潃34鍙狟OSS", "beyongd": 3, "type": 13, "value": [34], "rewards": [[11, 10]], "pre": 8032}, "8034": {"id": 8034, "desc": "鍑绘潃35鍙狟OSS", "beyongd": 3, "type": 13, "value": [35], "rewards": [[11, 10]], "pre": 8033}, "8035": {"id": 8035, "desc": "鍑绘潃36鍙狟OSS", "beyongd": 3, "type": 13, "value": [36], "rewards": [[11, 10]], "pre": 8034}, "8036": {"id": 8036, "desc": "鍑绘潃37鍙狟OSS", "beyongd": 3, "type": 13, "value": [37], "rewards": [[11, 10]], "pre": 8035}, "8037": {"id": 8037, "desc": "鍑绘潃38鍙狟OSS", "beyongd": 3, "type": 13, "value": [38], "rewards": [[11, 10]], "pre": 8036}, "8038": {"id": 8038, "desc": "鍑绘潃39鍙狟OSS", "beyongd": 3, "type": 13, "value": [39], "rewards": [[11, 10]], "pre": 8037}, "8039": {"id": 8039, "desc": "鍑绘潃40鍙狟OSS", "beyongd": 3, "type": 13, "value": [40], "rewards": [[11, 10]], "pre": 8038}, "8040": {"id": 8040, "desc": "鍑绘潃41鍙狟OSS", "beyongd": 3, "type": 13, "value": [41], "rewards": [[11, 10]], "pre": 8039}, "8041": {"id": 8041, "desc": "鍑绘潃42鍙狟OSS", "beyongd": 3, "type": 13, "value": [42], "rewards": [[11, 10]], "pre": 8040}, "8042": {"id": 8042, "desc": "鍑绘潃43鍙狟OSS", "beyongd": 3, "type": 13, "value": [43], "rewards": [[11, 10]], "pre": 8041}, "8043": {"id": 8043, "desc": "鍑绘潃44鍙狟OSS", "beyongd": 3, "type": 13, "value": [44], "rewards": [[11, 10]], "pre": 8042}, "8044": {"id": 8044, "desc": "鍑绘潃45鍙狟OSS", "beyongd": 3, "type": 13, "value": [45], "rewards": [[11, 10]], "pre": 8043}, "8045": {"id": 8045, "desc": "鍑绘潃46鍙狟OSS", "beyongd": 3, "type": 13, "value": [46], "rewards": [[11, 10]], "pre": 8044}, "8046": {"id": 8046, "desc": "鍑绘潃47鍙狟OSS", "beyongd": 3, "type": 13, "value": [47], "rewards": [[11, 10]], "pre": 8045}, "8047": {"id": 8047, "desc": "鍑绘潃48鍙狟OSS", "beyongd": 3, "type": 13, "value": [48], "rewards": [[11, 10]], "pre": 8046}, "8048": {"id": 8048, "desc": "鍑绘潃49鍙狟OSS", "beyongd": 3, "type": 13, "value": [49], "rewards": [[11, 10]], "pre": 8047}, "8049": {"id": 8049, "desc": "鍑绘潃50鍙狟OSS", "beyongd": 3, "type": 13, "value": [50], "rewards": [[11, 10]], "pre": 8048}, "8050": {"id": 8050, "desc": "鍑绘潃51鍙狟OSS", "beyongd": 3, "type": 13, "value": [51], "rewards": [[11, 10]], "pre": 8049}, "8051": {"id": 8051, "desc": "鍑绘潃52鍙狟OSS", "beyongd": 3, "type": 13, "value": [52], "rewards": [[11, 10]], "pre": 8050}, "8052": {"id": 8052, "desc": "鍑绘潃53鍙狟OSS", "beyongd": 3, "type": 13, "value": [53], "rewards": [[11, 10]], "pre": 8051}, "8053": {"id": 8053, "desc": "鍑绘潃54鍙狟OSS", "beyongd": 3, "type": 13, "value": [54], "rewards": [[11, 10]], "pre": 8052}, "8054": {"id": 8054, "desc": "鍑绘潃55鍙狟OSS", "beyongd": 3, "type": 13, "value": [55], "rewards": [[11, 10]], "pre": 8053}, "8055": {"id": 8055, "desc": "鍑绘潃56鍙狟OSS", "beyongd": 3, "type": 13, "value": [56], "rewards": [[11, 10]], "pre": 8054}, "8056": {"id": 8056, "desc": "鍑绘潃57鍙狟OSS", "beyongd": 3, "type": 13, "value": [57], "rewards": [[11, 10]], "pre": 8055}, "8057": {"id": 8057, "desc": "鍑绘潃58鍙狟OSS", "beyongd": 3, "type": 13, "value": [58], "rewards": [[11, 10]], "pre": 8056}, "8058": {"id": 8058, "desc": "鍑绘潃59鍙狟OSS", "beyongd": 3, "type": 13, "value": [59], "rewards": [[11, 10]], "pre": 8057}, "8059": {"id": 8059, "desc": "鍑绘潃60鍙狟OSS", "beyongd": 3, "type": 13, "value": [60], "rewards": [[11, 10]], "pre": 8058}}