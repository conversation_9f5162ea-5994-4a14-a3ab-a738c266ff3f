const fs = require('fs');
const path = require('path');
const iconv = require('iconv-lite');

// 设置输入和输出目录
const csvDir = './csv';
const jsonDir = './json';

// 确保输出目录存在
if (!fs.existsSync(jsonDir)) {
    fs.mkdirSync(jsonDir, { recursive: true });
}

// 检测文件编码
function detectEncoding(buffer, fileName = '') {
    // 根据已知信息：只有 buff.csv 是 GBK 编码，其余都是 UTF-8
    const lowerFileName = fileName.toLowerCase();

    // 检查BOM
    if (buffer.length >= 3 && buffer[0] === 0xEF && buffer[1] === 0xBB && buffer[2] === 0xBF) {
        return 'utf8';
    }

    // 明确指定：只有 buff.csv 使用 GBK 编码
    if (lowerFileName === 'buff.csv') {
        return 'gbk';
    }

    // 其他所有文件都使用 UTF-8 编码
    return 'utf8';
}

// 读取并转换文件内容
function readFileWithEncoding(filePath) {
    const buffer = fs.readFileSync(filePath);
    const fileName = path.basename(filePath);
    const encoding = detectEncoding(buffer, fileName);

    console.log(`Detected encoding for ${fileName}: ${encoding}`);

    let content;
    if (encoding === 'gbk') {
        content = iconv.decode(buffer, 'gbk');
    } else {
        content = iconv.decode(buffer, 'utf8');
    }

    // 统一换行符为 \n
    content = content.replace(/\r\n/g, '\n').replace(/\r/g, '\n');

    return content;
}

// 获取所有CSV文件
function getCsvFiles() {
    try {
        const files = fs.readdirSync(csvDir);
        return files.filter(file => file.endsWith('.csv'));
    } catch (err) {
        console.error(`Error reading csv directory: ${err.message}`);
        return [];
    }
}

// 解析CSV行，处理引号和逗号
function parseCSVLine(line) {
    const result = [];
    let current = '';
    let inQuotes = false;
    let i = 0;
    
    while (i < line.length) {
        const char = line[i];
        
        if (char === '"') {
            if (inQuotes && line[i + 1] === '"') {
                // 转义的引号
                current += '"';
                i += 2;
            } else {
                // 切换引号状态
                inQuotes = !inQuotes;
                i++;
            }
        } else if (char === ',' && !inQuotes) {
            // 字段分隔符
            result.push(current);
            current = '';
            i++;
        } else {
            current += char;
            i++;
        }
    }
    
    // 添加最后一个字段
    result.push(current);
    return result;
}

// 处理单个CSV文件
function processCsvFile(csvFile) {
    const csvPath = path.join(csvDir, csvFile);
    console.log(`Processing file: ${csvFile}`);

    try {
        // 读取CSV内容（自动检测编码）
        const csvContent = readFileWithEncoding(csvPath);
        
        // 更智能的行分割，考虑引号内的换行符
        const lines = [];
        let currentLine = '';
        let inQuotes = false;
        
        for (let i = 0; i < csvContent.length; i++) {
            const char = csvContent[i];
            
            if (char === '"') {
                if (inQuotes && csvContent[i + 1] === '"') {
                    // 转义的引号
                    currentLine += '""';
                    i++;
                } else {
                    // 切换引号状态
                    inQuotes = !inQuotes;
                    currentLine += char;
                }
            } else if (char === '\n' && !inQuotes) {
                // 行结束（不在引号内）
                if (currentLine.trim() !== '') {
                    lines.push(currentLine);
                }
                currentLine = '';
            } else {
                currentLine += char;
            }
        }
        
        // 添加最后一行
        if (currentLine.trim() !== '') {
            lines.push(currentLine);
        }
        
        if (lines.length < 2) {
            console.log(`Warning: ${csvFile} does not contain enough data`);
            return;
        }
        
        // 解析标题行
        const headers = parseCSVLine(lines[0]);
        const idColumn = headers[0]; // 第一列是ID列
        const dataColumns = headers.slice(1); // 其余列是数据列
        
        // 创建JSON对象
        const jsonData = {};
        
        // 处理每一行数据
        for (let i = 1; i < lines.length; i++) {
            const values = parseCSVLine(lines[i]);
            
            if (values.length !== headers.length) {
                console.log(`Warning: Line ${i + 1} in ${csvFile} has incorrect number of columns`);
                continue;
            }
            
            const id = values[0];
            const dataObject = {};
            
            // 构建数据对象
            for (let j = 1; j < values.length; j++) {
                const columnName = dataColumns[j - 1];
                let value = values[j];
                
                // 处理空值
                if (value === '' || value === null || value === undefined) {
                    continue; // 跳过空值，不添加到对象中
                }
                
                // 尝试转换数值、布尔值和数组
                if (value.toLowerCase() === 'true') {
                    value = true;
                } else if (value.toLowerCase() === 'false') {
                    value = false;
                } else if (!isNaN(value) && !isNaN(parseFloat(value)) && value.trim() !== '') {
                    const numValue = parseFloat(value);
                    // 检查是否为整数
                    if (Number.isInteger(numValue)) {
                        value = parseInt(value);
                    } else {
                        value = numValue;
                    }
                } else if (value.startsWith('[') && value.endsWith(']')) {
                    // 解析JSON数组格式（统一的数组格式）
                    try {
                        const parsed = JSON.parse(value);
                        if (Array.isArray(parsed)) {
                            value = parsed;
                        }
                    } catch (e) {
                        // 保持原值
                    }
                }
                
                dataObject[columnName] = value;
            }
            
            // 只有当数据对象不为空时才添加到JSON中
            if (Object.keys(dataObject).length > 0) {
                jsonData[id] = dataObject;
            }
        }
        
        // 创建JSON文件名
        const jsonFileName = path.basename(csvFile, '.csv') + '.json';
        const jsonPath = path.join(jsonDir, jsonFileName);
        
        // 写入JSON文件（UTF-8编码，CRLF换行符）
        const jsonContent = JSON.stringify(jsonData, null, 2);
        // 将LF换行符转换为CRLF
        const jsonContentWithCRLF = jsonContent.replace(/\n/g, '\r\n');
        fs.writeFileSync(jsonPath, jsonContentWithCRLF, 'utf8');
        console.log(`Created JSON file: ${jsonFileName}`);
        
    } catch (err) {
        console.error(`Error: Failed to process ${csvFile}: ${err.message}`);
    }
}

// 主函数
function main() {
    const csvFiles = getCsvFiles();
    
    if (csvFiles.length === 0) {
        console.log('No CSV files found in the csv directory');
        return;
    }
    
    csvFiles.forEach(processCsvFile);
    console.log('All CSV files have been converted!');
}

// 执行主函数
main();