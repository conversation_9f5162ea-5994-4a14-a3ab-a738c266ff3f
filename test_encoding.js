const fs = require('fs');

// 测试文件的换行符格式
function testLineEndings(filePath) {
    const buffer = fs.readFileSync(filePath);
    const content = buffer.toString('utf8');
    
    const crlfCount = (content.match(/\r\n/g) || []).length;
    const lfCount = (content.match(/(?<!\r)\n/g) || []).length;
    const crCount = (content.match(/\r(?!\n)/g) || []).length;
    
    console.log(`File: ${filePath}`);
    console.log(`CRLF (\\r\\n): ${crlfCount}`);
    console.log(`LF (\\n): ${lfCount}`);
    console.log(`CR (\\r): ${crCount}`);
    
    if (crlfCount > 0 && lfCount === 0 && crCount === 0) {
        console.log('✓ File uses CRLF line endings');
    } else if (lfCount > 0 && crlfCount === 0 && crCount === 0) {
        console.log('✓ File uses LF line endings');
    } else {
        console.log('⚠ File has mixed line endings');
    }
    console.log('---');
}

// 测试原始CSV文件和转换后的JSON文件
console.log('Testing line endings...\n');

testLineEndings('./csv/Buff.csv');
testLineEndings('./json/Buff.json');

// 测试编码
function testEncoding(filePath) {
    const buffer = fs.readFileSync(filePath);
    console.log(`File: ${filePath}`);
    console.log(`First 100 bytes:`, buffer.slice(0, 100));
    
    // 检查是否有中文字符
    const content = buffer.toString('utf8');
    const hasChinese = /[\u4e00-\u9fff]/.test(content.slice(0, 500));
    console.log(`Contains Chinese characters: ${hasChinese}`);
    
    if (hasChinese) {
        console.log(`Sample text: ${content.slice(0, 200)}`);
    }
    console.log('---');
}

console.log('\nTesting encoding...\n');
testEncoding('./csv/Buff.csv');
testEncoding('./json/Buff.json');
