const fs = require('fs');
const path = require('path');
const iconv = require('iconv-lite');

// 复制csvToJson.js中的相关函数
function detectEncoding(buffer) {
    // 检查BOM
    if (buffer.length >= 3 && buffer[0] === 0xEF && buffer[1] === 0xBB && buffer[2] === 0xBF) {
        return 'utf8';
    }
    
    // 简单的中文字符检测
    const sample = buffer.slice(0, Math.min(1000, buffer.length));
    let hasHighBytes = false;
    
    for (let i = 0; i < sample.length; i++) {
        if (sample[i] > 127) {
            hasHighBytes = true;
            break;
        }
    }
    
    if (!hasHighBytes) {
        return 'utf8'; // 纯ASCII，使用UTF-8
    }
    
    // 尝试用UTF-8解码，如果出现乱码则可能是GBK
    try {
        const utf8Text = iconv.decode(sample, 'utf8');
        // 检查是否包含UTF-8的替换字符（乱码标志）
        if (utf8Text.includes('�')) {
            return 'gbk';
        }
        return 'utf8';
    } catch (e) {
        return 'gbk';
    }
}

function readFileWithEncoding(filePath) {
    const buffer = fs.readFileSync(filePath);
    const encoding = detectEncoding(buffer);
    
    console.log(`Detected encoding for ${path.basename(filePath)}: ${encoding}`);
    
    let content;
    if (encoding === 'gbk') {
        content = iconv.decode(buffer, 'gbk');
    } else {
        content = iconv.decode(buffer, 'utf8');
    }
    
    // 统一换行符为 \n
    content = content.replace(/\r\n/g, '\n').replace(/\r/g, '\n');
    
    return content;
}

// 测试转换 Buff.csv
console.log('Testing Buff.csv conversion...\n');

const csvPath = './csv/Buff.csv';
const content = readFileWithEncoding(csvPath);

// 显示前几行内容
const lines = content.split('\n');
console.log('First 5 lines of converted content:');
for (let i = 0; i < Math.min(5, lines.length); i++) {
    console.log(`Line ${i + 1}: ${lines[i]}`);
}

console.log(`\nTotal lines: ${lines.length}`);
console.log(`Content length: ${content.length} characters`);

// 检查是否包含正确的中文字符
const chineseMatches = content.match(/[\u4e00-\u9fff]/g);
if (chineseMatches) {
    console.log(`\nFound ${chineseMatches.length} Chinese characters`);
    console.log('Sample Chinese text:', content.match(/[\u4e00-\u9fff]+/g)?.slice(0, 10));
} else {
    console.log('\nNo Chinese characters found');
}
