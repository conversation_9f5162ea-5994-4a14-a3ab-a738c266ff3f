# JSON-CSV 转换工具

这是一个用于在JSON和CSV格式之间进行双向转换的工具集。

## 功能特性

- **数组类型保持**：确保JSON中的数组在转换过程中保持数据类型
- **格式一致性**：所有数组在CSV中统一使用JSON格式，易于理解和编辑
- **智能类型识别**：自动识别并正确转换数值、布尔值、字符串和数组
- **复杂数组支持**：支持嵌套数组和混合类型数组
- **特殊字符处理**：正确处理包含逗号、引号和换行符的数据

## 文件说明

- `jsonToCsv.js` - 将JSON文件转换为CSV文件
- `csvToJson.js` - 将CSV文件转换为JSON文件
- `test_conversion.js` - 测试转换一致性的脚本

## 使用方法

### JSON转CSV
```bash
node jsonToCsv.js
```

### CSV转JSON
```bash
node csvToJson.js
```

### 测试转换一致性
```bash
node test_conversion.js
```

## 数组处理机制

### JSON到CSV转换
- **统一JSON格式**：所有数组类型都使用JSON格式存储，保持一致性
  - 简单数组：`[5, 3]` → `[5,3]`
  - 单元素数组：`[2]` → `[2]`
  - 复杂数组：`[[13,3,0.1],[11,200,0.05]]` → `[[13,3,0.1],[11,200,0.05]]`

### CSV到JSON转换
- **统一JSON解析**：识别以`[`开头和`]`结尾的字符串并解析为数组
- **智能类型恢复**：自动将JSON格式字符串转换回对应的数组类型
- **格式一致性**：CSV中的数组格式易于理解和编辑

## 修复的问题

在之前的版本中，数组字段在JSON→CSV→JSON的转换过程中会丢失类型信息：

**修复前:**
```javascript
// 原始JSON
"fightCount": [5, 3]

// CSV中
"5,3"

// 转换回JSON后
"fightCount": "5,3"  // ❌ 变成了字符串
```

**修复后:**
```javascript
// 原始JSON
"fightCount": [5, 3]

// CSV中
"5,3"

// 转换回JSON后
"fightCount": [5, 3]  // ✅ 保持数组类型
```

## 支持的数据类型

- **数值**: 自动识别整数和浮点数
- **布尔值**: `true`/`false` 字符串自动转换
- **字符串**: 保持原始字符串格式
- **简单数组**: 数值数组用逗号分隔
- **复杂数组**: 嵌套数组用JSON格式存储

## 目录结构

```
configs/
├── csv/          # CSV文件目录
├── json/         # JSON文件目录
├── jsonToCsv.js  # JSON转CSV脚本
├── csvToJson.js  # CSV转JSON脚本
├── test_conversion.js  # 测试脚本
└── README.md     # 说明文档
```

## 注意事项

1. 确保JSON文件格式正确，第一级键作为ID，第二级为数据对象
2. CSV文件第一列必须是ID列，第一行为标题行
3. 包含逗号的字符串会自动用引号包围
4. 空值会被跳过，不会添加到输出中

## 测试验证

运行 `test_conversion.js` 可以验证转换的一致性，该脚本会：
1. 备份原始JSON文件
2. 执行JSON→CSV→JSON的完整转换流程
3. 比较转换前后的数据结构和类型
4. 恢复原始文件

测试通过表示数据在转换过程中保持了完全的一致性。