{"1000": {"id": 1000, "roleName": "符箓", "desc": "子弹流yyds", "type": 1, "rarity": 1, "unlock": 1, "Count": 0, "icon": "v1/images/equip/item_20011", "isShow": 1}, "1001": {"id": 1001, "roleName": "起爆符", "desc": "爆炸", "type": 1, "rarity": 1, "unlock": 1, "Count": 0, "icon": "v1/images/equip/item_20016", "isShow": 1}, "1002": {"id": 1002, "roleName": "阴阳镜", "desc": "激光连线", "type": 1, "rarity": 1, "unlock": 1, "Count": 0, "icon": "v1/images/equip/item_20054", "isShow": 0}, "1003": {"id": 1003, "roleName": "闪雷鞭", "desc": "闪电连击", "type": 1, "rarity": 2, "unlock": 1, "Count": 2, "icon": "v1/images/equip/item_20034", "isShow": 1}, "1004": {"id": 1004, "roleName": "青云剑", "desc": "剧毒缠身", "type": 1, "rarity": 2, "unlock": 1, "Count": 0, "icon": "v1/images/equip/item_20145", "isShow": 1}, "1005": {"id": 1005, "roleName": "弑神枪", "desc": "爆裂枪", "type": 1, "rarity": 2, "unlock": 1, "Count": 0, "icon": "v1/images/equip/item_20026", "isShow": 1}, "1006": {"id": 1006, "roleName": "招魂幡", "desc": "百鬼夜行", "type": 1, "rarity": 2, "unlock": 1, "Count": 0, "icon": "v1/images/equip/item_20046", "isShow": 1}, "1007": {"id": 1007, "roleName": "风火轮", "desc": "火焰冲击", "type": 1, "rarity": 2, "unlock": 1, "Count": 0, "icon": "v1/images/equip/item_20025", "isShow": 1}, "1008": {"id": 1008, "roleName": "雷锲", "desc": "雷霆之力", "type": 1, "rarity": 2, "unlock": 1, "Count": 0, "icon": "v1/images/equip/item_20153", "isShow": 1}, "1009": {"id": 1009, "roleName": "玄天剑", "desc": "御剑攻击", "type": 1, "rarity": 3, "unlock": 1, "Count": 4, "icon": "v1/images/equip/item_20143", "isShow": 1}, "1010": {"id": 1010, "roleName": "天寒重剑", "desc": "重剑挥舞", "type": 1, "rarity": 3, "unlock": 1, "Count": 6, "icon": "v1/images/equip/item_20065", "isShow": 1}, "1011": {"id": 1011, "roleName": "番天印", "desc": "一印永恒", "type": 1, "rarity": 3, "unlock": 1, "Count": 12, "icon": "v1/images/equip/item_20164", "isShow": 1}, "1012": {"id": 1012, "roleName": "紫金葫芦", "desc": "黑洞吸", "type": 1, "rarity": 3, "unlock": 1, "Count": 14, "icon": "v1/images/equip/item_20023", "isShow": 1}, "1013": {"id": 1013, "roleName": "定海神针", "desc": "棍棒旋转", "type": 1, "rarity": 3, "unlock": 1, "Count": 8, "icon": "v1/images/equip/item_20173", "isShow": 1}, "1014": {"id": 1014, "roleName": "万灵旗", "desc": "万灵齐奔", "type": 1, "rarity": 3, "unlock": 1, "Count": 10, "icon": "v1/images/equip/item_20045", "isShow": 1}, "1015": {"id": 1015, "roleName": "三光琉璃瓶", "desc": "脉冲光速炮!", "type": 1, "rarity": 3, "unlock": 1, "Count": 16, "icon": "v1/images/equip/item_20084", "isShow": 1}, "3000": {"id": 3000, "roleName": "小帅", "type": 3, "rarity": 2, "unlock": 2, "Count": 20, "icon": "img/lobby/role01", "isShow": 1, "uiSpine": "bones/role/build01a", "spine": "bones/role/build01b"}, "3001": {"id": 3001, "roleName": "小黑", "type": 3, "rarity": 3, "unlock": 2, "Count": 40, "icon": "img/lobby/role03", "isShow": 1, "uiSpine": "bones/role/build03a", "spine": "bones/role/build03b"}, "3002": {"id": 3002, "roleName": "小美", "type": 3, "rarity": 2, "unlock": 2, "Count": 20, "icon": "img/lobby/role05", "isShow": 1, "uiSpine": "bones/role/build05a", "spine": "bones/role/build05b"}, "3003": {"id": 3003, "roleName": "大壮", "rarity": 3, "unlock": 2, "Count": 40, "isShow": 1, "spine": "bones/role/build02a"}, "3004": {"id": 3004, "roleName": "二蛋", "rarity": 2, "unlock": 2, "Count": 20, "isShow": 1, "spine": "bones/role/build06a"}, "3005": {"id": 3005, "roleName": "时空行者", "rarity": 3, "unlock": 2, "Count": 80, "isShow": 1, "spine": "bones/role/battery07"}, "3006": {"id": 3006, "roleName": "百戏之王", "rarity": 4, "unlock": 2, "Count": 80, "isShow": 1, "spine": "bones/role/battery08"}, "3007": {"id": 3007, "roleName": "暗杀之神", "rarity": 4, "unlock": 2, "Count": 80, "isShow": 1, "spine": "bones/role/battery08"}, "3008": {"id": 3008, "roleName": "陨星", "rarity": 4, "unlock": 2, "Count": 80, "isShow": 1, "spine": "bones/role/battery08"}, "4000": {"id": 4000, "roleName": "贪婪宝石", "desc": "只能镶嵌在4级进阶装备，镶嵌后可抽取装备专属词条", "type": 4, "rarity": 4, "icon": "img/ModeBackpackHero/gemicon/gem_tlbs", "isShow": 0, "spine": "bones/gem/buff_bs6", "grid": 1}, "4001": {"id": 4001, "roleName": "幻化宝石", "desc": "无法镶嵌，放入背包后，每回合可以变化成其他宝石", "type": 4, "rarity": 3, "icon": "img/ModeBackpackHero/gemicon/gem_hhbs", "isShow": 1, "spine": "bones/gem/buff_bs4", "grid": 1}, "4002": {"id": 4002, "roleName": "命运宝石", "desc": "镶嵌后，有较大概率提升2级合成等级，但也有概率失败销毁当前武器", "type": 4, "rarity": 3, "icon": "img/ModeBackpackHero/gemicon/gem_mybs", "isShow": 1, "spine": "bones/gem/buff_bs5", "grid": 1}, "4003": {"id": 4003, "roleName": "克隆宝石", "desc": "无法镶嵌，有较大概率可克隆其他宝石，但也有概率失败，原宝石不会销毁", "type": 4, "rarity": 3, "icon": "img/ModeBackpackHero/gemicon/gem_klbs", "isShow": 1, "spine": "bones/gem/buff_bs2", "grid": 1}, "4004": {"id": 4004, "roleName": "献祭宝石", "desc": "无法镶嵌，献祭5次后消失，献祭2级或3级装备时，可获得对应数量的稀有或史诗，传说宝石", "type": 4, "rarity": 3, "icon": "img/ModeBackpackHero/gemicon/gem_xjbs", "isShow": 1, "spine": "bones/gem/buff_bs1", "grid": 1}, "4005": {"id": 4005, "roleName": "复制宝石", "desc": "镶嵌后可以获得相同的装备，但装备的伤害/生命减少50%", "type": 4, "rarity": 4, "icon": "img/ModeBackpackHero/gemicon/gem_fzbs", "isShow": 1, "spine": "bones/gem/buff_bs3", "grid": 1}, "4006": {"id": 4006, "roleName": "攻击宝石", "desc": "攻击+30%", "type": 4, "rarity": 2, "icon": "img/ModeBackpackHero/gemicon/gem_gjbs", "isShow": 1, "spine": "bones/gem/buff_bs8", "grid": 1}, "4007": {"id": 4007, "roleName": "生命宝石", "desc": "生命+50%", "type": 4, "rarity": 2, "icon": "img/ModeBackpackHero/gemicon/gem_smbs", "isShow": 1, "spine": "bones/gem/buff_bs7", "grid": 1}, "4008": {"id": 4008, "roleName": "加速宝石", "desc": "装备速度+2%", "type": 4, "rarity": 2, "icon": "img/ModeBackpackHero/gemicon/gem_jsbs", "isShow": 1, "spine": "bones/gem/buff_bs9", "grid": 1}, "4009": {"id": 4009, "roleName": "暴击宝石", "desc": "暴击率+5%", "type": 4, "rarity": 2, "icon": "img/ModeBackpackHero/gemicon/gem_bjbs", "isShow": 1, "spine": "bones/gem/buff_bs10", "grid": 1}, "4010": {"id": 4010, "roleName": "缩小宝石", "desc": "有概率把装备缩小至1格,如果失败，宝石销毁，不占镶嵌孔", "type": 4, "rarity": 4, "icon": "img/ModeBackpackHero/gemicon/gem_sxbs", "isShow": 1, "spine": "bones/gem/buff_bs11", "grid": 1}, "4011": {"id": 4011, "roleName": "招财宝石", "desc": "只能镶嵌在钱袋上，每次钱袋结算时，可额外获得10~100的银币", "type": 4, "rarity": 3, "icon": "img/ModeBackpackHero/gemicon/gem_zcmbs", "isShow": 1, "spine": "bones/gem/buff_bs12", "grid": 1}, "4012": {"id": 4012, "roleName": "母鸡宝石", "desc": "放入背包后每回合可下蛋获得各种奖励", "type": 4, "rarity": 4, "icon": "img/ModeBackpackHero/gemicon/gem_mjbs", "isShow": 1, "spine": "bones/gem/buff_bs12", "grid": 1}, "4013": {"id": 4013, "roleName": "背包宝石", "desc": "镶嵌到背包上可增加10格背包上限", "type": 4, "rarity": 4, "icon": "img/ModeBackpackHero/gemicon/gem_bbbs", "isShow": 1, "spine": "bones/gem/buff_bs12", "grid": 1}, "4014": {"id": 4014, "roleName": "膨胀宝石", "desc": "武器弹道体积+10%", "type": 4, "rarity": 2, "icon": "img/ModeBackpackHero/gemicon/gem_pzbs", "isShow": 1, "spine": "bones/gem/buff_bs12", "grid": 1}, "4015": {"id": 4015, "roleName": "月饼宝石", "desc": "只能镶嵌在3级武器上，有概率变化为传说武器饼之花", "type": 4, "rarity": 4, "icon": "img/ModeBackpackHero/gemicon/gem_ybbs", "isShow": 1, "spine": "bones/gem/buff_bs12", "grid": 1}, "4016": {"id": 4016, "roleName": "连击宝石", "desc": "武器弹道数量+2", "type": 4, "rarity": 3, "icon": "img/ModeBackpackHero/gemicon/gem_ljbs", "isShow": 1, "spine": "bones/gem/buff_bs21", "grid": 1}, "4017": {"id": 4017, "roleName": "璀璨宝石", "desc": "只能镶嵌在4级进阶武器上,伤害+100%，速度+30%，暴击率+50%，暴击伤害+100%", "type": 4, "rarity": 4, "icon": "img/ModeBackpackHero/gemicon/gem_ccbs", "isShow": 1, "spine": "bones/gem/buff_bs22", "grid": 1}, "10000": {"id": 10000, "roleName": "钱袋", "rarity": 1, "unlock": 1, "Count": 0, "icon": "v1/images/equip/item_20132", "isShow": 0, "grid": 1}, "20000": {"id": 20000, "roleName": "1格子", "type": 2, "rarity": 1, "unlock": 1, "Count": 0, "isShow": 0, "grid": 1}, "20001": {"id": 20001, "roleName": "2格子", "type": 2, "rarity": 1, "unlock": 1, "Count": 0, "isShow": 0, "grid": 21}, "20002": {"id": 20002, "roleName": "2格子", "type": 2, "rarity": 1, "unlock": 1, "Count": 0, "isShow": 0, "grid": 22}, "20003": {"id": 20003, "roleName": "3格子", "type": 2, "rarity": 2, "unlock": 1, "Count": 0, "isShow": 0, "grid": 31}, "20004": {"id": 20004, "roleName": "3格子", "type": 2, "rarity": 2, "unlock": 1, "Count": 0, "isShow": 0, "grid": 32}, "20005": {"id": 20005, "roleName": "3格子", "type": 2, "rarity": 2, "unlock": 1, "Count": 0, "isShow": 0, "grid": 33}, "20006": {"id": 20006, "roleName": "3格子", "type": 2, "rarity": 2, "unlock": 1, "Count": 0, "isShow": 0, "grid": 34}, "20007": {"id": 20007, "roleName": "3格子", "type": 2, "rarity": 2, "unlock": 1, "Count": 0, "isShow": 0, "grid": 35}, "20008": {"id": 20008, "roleName": "3格子", "type": 2, "rarity": 2, "unlock": 1, "Count": 0, "isShow": 0, "grid": 36}, "20009": {"id": 20009, "roleName": "4格子", "type": 2, "rarity": 2, "unlock": 1, "Count": 0, "isShow": 0, "grid": 4}, "20010": {"id": 20010, "roleName": "4格子", "type": 2, "rarity": 2, "unlock": 1, "Count": 0, "isShow": 0, "grid": 41}, "20011": {"id": 20011, "roleName": "4格子", "type": 2, "rarity": 2, "unlock": 1, "Count": 0, "isShow": 0, "grid": 42}, "30000": {"id": 30000, "roleName": "科学家", "type": 20, "rarity": 2, "unlock": 2, "Count": 20, "icon": "img/ModeTKnife/icon_Professor", "isShow": 1, "uiSpine": "bones/role/role_Professor", "spine": "bones/role/build01b", "startSkill": 4000}, "30100": {"id": 30100, "roleName": "小帅子弹落", "desc": "子弹飞小游戏", "type": 20, "rarity": 2, "unlock": 2, "Count": 20, "icon": "img/lobby/role01", "isShow": 1, "uiSpine": "bones/role/role_bulletgame", "spine": "bones/role/role_bulletgame", "startSkill": 5000}, "30200": {"id": 30200, "roleName": "小帅弹来弹去", "type": 20, "rarity": 2, "unlock": 2, "Count": 20, "icon": "img/ModeTKnife/icon_zhujue", "isShow": 1, "uiSpine": "bones/role/role_bulletgame", "spine": "bones/role/role_bulletgame", "startSkill": 3000}, "30300": {"id": 30300, "roleName": "小帅末日屠龙", "type": 20, "rarity": 2, "unlock": 2, "Count": 20, "isShow": 1, "uiSpine": "bones/role/build01c", "spine": "bones/role/build01c", "startSkill": 6000}, "30301": {"id": 30301, "roleName": "小帅末日屠龙筋斗云", "type": 20, "rarity": 2, "unlock": 2, "Count": 20, "isShow": 1, "uiSpine": "bones/role/build01d", "spine": "bones/role/build01d", "startSkill": 6000}, "30302": {"id": 30302, "roleName": "小帅末日屠龙石头龙", "type": 20, "rarity": 2, "unlock": 2, "Count": 20, "isShow": 1, "uiSpine": "bones/role/build01e", "spine": "bones/role/build01e", "startSkill": 9080}, "30303": {"id": 30303, "roleName": "末日屠龙公主", "type": 20, "rarity": 2, "unlock": 2, "Count": 20, "isShow": 1, "uiSpine": "bones/role/role_princess", "spine": "bones/role/role_princess"}, "30304": {"id": 30304, "roleName": "小帅末日屠龙(救公主无敌)", "type": 20, "rarity": 2, "unlock": 2, "Count": 20, "isShow": 1, "uiSpine": "bones/role/build01c", "spine": "bones/role/build01c", "startSkill": 6000, "startBuff": 9999}, "30305": {"id": 30305, "roleName": "小帅末日屠龙(无初始技能)", "type": 20, "rarity": 2, "unlock": 2, "Count": 20, "isShow": 1, "uiSpine": "bones/role/build01c", "spine": "bones/role/build01c"}, "30306": {"id": 30306, "roleName": "小帅末日屠龙", "type": 20, "rarity": 2, "unlock": 2, "Count": 20, "isShow": 1, "uiSpine": "bones/role/build01c", "spine": "bones/role/build01c", "startSkill": 6820}, "30307": {"id": 30307, "roleName": "末日屠龙弓箭手", "type": 20, "rarity": 2, "unlock": 2, "Count": 20, "isShow": 1, "uiSpine": "bones/role/roleArcher", "spine": "bones/role/roleArcher", "startSkill": 6780}, "30308": {"id": 30308, "roleName": "末日屠龙枪手", "type": 20, "rarity": 2, "unlock": 2, "Count": 20, "isShow": 1, "uiSpine": "bones/role/roleGunner", "spine": "bones/role/roleGunner", "startSkill": 6800}, "30309": {"id": 30309, "roleName": "末日屠龙弓法师", "type": 20, "rarity": 2, "unlock": 2, "Count": 20, "isShow": 1, "uiSpine": "bones/role/roleMage", "spine": "bones/role/roleMage", "startSkill": 6820}, "30310": {"id": 30310, "roleName": "末日屠龙人质", "type": 20, "rarity": 2, "unlock": 2, "Count": 20, "isShow": 1, "uiSpine": "bones/role/role_captive", "spine": "bones/role/role_captive"}, "30311": {"id": 30311, "roleName": "小帅末日屠龙(救公主无敌)", "type": 20, "rarity": 2, "unlock": 2, "Count": 20, "isShow": 1, "uiSpine": "bones/role/build01c", "spine": "bones/role/build01c", "startBuff": 9999}, "30312": {"id": 30312, "roleName": "卡皮巴拉末日屠龙石头龙", "type": 20, "rarity": 2, "unlock": 2, "Count": 20, "isShow": 1, "uiSpine": "bones/role/kpbl", "spine": "bones/role/kpbl", "startSkill": 9120}, "30313": {"id": 30313, "roleName": "卡皮巴拉圣诞末日屠龙石头龙", "type": 20, "rarity": 2, "unlock": 2, "Count": 20, "isShow": 1, "uiSpine": "bones/role/sdkpbl", "spine": "bones/role/sdkpbl", "startSkill": 9120}, "30400": {"id": 30400, "roleName": "小帅尸潮防线", "type": 20, "rarity": 2, "unlock": 2, "Count": 20, "isShow": 1, "uiSpine": "bones/role/build01b", "spine": "bones/role/build01b", "startSkill": 7000}, "30500": {"id": 30500, "roleName": "小帅一弹射穿", "type": 20, "rarity": 2, "unlock": 2, "Count": 20, "icon": "img/ModeTKnife/icon_zhujue", "isShow": 1, "uiSpine": "bones/role/role_bulletgame", "spine": "bones/role/role_bulletgame", "startSkill": 3000}, "30600": {"id": 30600, "roleName": "小帅守大桥（击退）", "type": 20, "rarity": 2, "unlock": 2, "Count": 20, "isShow": 1, "uiSpine": "bones/role/build01b", "spine": "bones/role/build01b", "startSkill": 9020}, "30700": {"id": 30700, "roleName": "小帅守大桥", "type": 20, "rarity": 2, "unlock": 2, "Count": 20, "isShow": 1, "uiSpine": "bones/role/build01b", "spine": "bones/role/build01b", "startSkill": 9000}, "30800": {"id": 30800, "roleName": "大聪明龙神战争", "type": 20, "rarity": 2, "unlock": 2, "Count": 20, "isShow": 1, "uiSpine": "bones/monster/long_blue_f;img/ModeChains/body3_f;img/ModeChains/tail3_f", "spine": "bones/monster/long_sl;img/ModeChains/body_sl;img/ModeChains/tail_sl;bones/monster/long_red_f;img/ModeChains/body1_f;img/ModeChains/tail1"}}